import { IndicatorPerformance } from "./historical-analyzer.ts";
import { DataManager } from "./data-manager.ts";
import { config } from "./env.ts";

export interface IndicatorWeight {
  indicator: string;
  weight: number;
  confidence: number;
  performance: {
    winRate: number;
    avgReturn: number;
    sharpeRatio: number;
    consistency: number;
  };
  lastUpdated: number;
}

export interface SymbolWeights {
  symbol: string;
  indicators: Record<string, IndicatorWeight>;
  overallScore: number;
  lastUpdated: number;
}

export interface WeightingStrategy {
  name: string;
  description: string;
  calculate: (performance: IndicatorPerformance[]) => number;
}

export class WeightCalculator {
  private dataManager: DataManager;
  private strategies: WeightingStrategy[];
  private readonly minSampleSize = 10;
  private readonly decayFactor = 0.95;
  private readonly performanceWindow = config.daysBack;

  constructor(dataManager: DataManager) {
    this.dataManager = dataManager;
    this.strategies = this.initializeStrategies();
  }

  private initializeStrategies(): WeightingStrategy[] {
    return [
      {
        name: 'sharpe_ratio',
        description: 'Weight based on risk-adjusted returns (Sharpe ratio)',
        calculate: (performance: IndicatorPerformance[]) => {
          const avgSharpe = performance.reduce((sum, p) => sum + p.sharpeRatio, 0) / performance.length;
          return Math.max(0, Math.min(1, (avgSharpe + 1) / 2)); // Normalize to 0-1
        }
      },
      {
        name: 'win_rate',
        description: 'Weight based on win rate',
        calculate: (performance: IndicatorPerformance[]) => {
          const avgWinRate = performance.reduce((sum, p) => sum + p.winRate, 0) / performance.length;
          return avgWinRate;
        }
      },
      {
        name: 'profitability',
        description: 'Weight based on total profitability',
        calculate: (performance: IndicatorPerformance[]) => {
          const totalProfit = performance.reduce((sum, p) => sum + p.profitability, 0);
          return Math.max(0, Math.min(1, (totalProfit + 0.5) / 1)); // Normalize around 0
        }
      },
      {
        name: 'consistency',
        description: 'Weight based on consistency across different market conditions',
        calculate: (performance: IndicatorPerformance[]) => {
          if (performance.length < 2) return 0.5;
          
          const winRates = performance.map(p => p.winRate);
          const avgWinRate = winRates.reduce((sum, wr) => sum + wr, 0) / winRates.length;
          const variance = winRates.reduce((sum, wr) => sum + Math.pow(wr - avgWinRate, 2), 0) / winRates.length;
          const consistency = 1 - Math.sqrt(variance);
          
          return Math.max(0, Math.min(1, consistency));
        }
      },
      {
        name: 'risk_adjusted',
        description: 'Weight based on return per unit of maximum drawdown',
        calculate: (performance: IndicatorPerformance[]) => {
          const avgReturn = performance.reduce((sum, p) => sum + p.avgReturn, 0) / performance.length;
          const avgDrawdown = performance.reduce((sum, p) => sum + p.maxDrawdown, 0) / performance.length;
          
          if (avgDrawdown === 0) return avgReturn > 0 ? 1 : 0;
          const riskAdjustedReturn = avgReturn / avgDrawdown;
          
          return Math.max(0, Math.min(1, (riskAdjustedReturn + 1) / 2));
        }
      }
    ];
  }

  async calculateIndicatorWeights(performanceData: IndicatorPerformance[]): Promise<Record<string, SymbolWeights>> {
    console.log("⚖️ Calculating indicator weights...");
    
    const symbolWeights: Record<string, SymbolWeights> = {};
    
    // Group performance data by symbol
    const performanceBySymbol = this.groupPerformanceBySymbol(performanceData);
    
    for (const [symbol, symbolPerformance] of Object.entries(performanceBySymbol)) {
      symbolWeights[symbol] = await this.calculateSymbolWeights(symbol, symbolPerformance);
    }

    // Save weights to cache
    await this.dataManager.saveWeights(symbolWeights);
    
    console.log(`✅ Calculated weights for ${Object.keys(symbolWeights).length} symbols`);
    return symbolWeights;
  }

  private groupPerformanceBySymbol(performanceData: IndicatorPerformance[]): Record<string, IndicatorPerformance[]> {
    const grouped: Record<string, IndicatorPerformance[]> = {};
    
    for (const performance of performanceData) {
      if (!grouped[performance.symbol]) {
        grouped[performance.symbol] = [];
      }
      grouped[performance.symbol].push(performance);
    }
    
    return grouped;
  }

  private async calculateSymbolWeights(symbol: string, performanceData: IndicatorPerformance[]): Promise<SymbolWeights> {
    const indicatorWeights: Record<string, IndicatorWeight> = {};
    
    // Group by indicator type
    const performanceByIndicator = this.groupPerformanceByIndicator(performanceData);
    
    for (const [indicator, indicatorPerformance] of Object.entries(performanceByIndicator)) {
      // Filter out indicators with insufficient sample size
      const validPerformance = indicatorPerformance.filter(p => p.sampleSize >= this.minSampleSize);
      
      if (validPerformance.length === 0) {
        indicatorWeights[indicator] = this.createDefaultWeight(indicator);
        continue;
      }

      const weight = this.calculateIndicatorWeight(validPerformance);
      const confidence = this.calculateConfidence(validPerformance);
      
      indicatorWeights[indicator] = {
        indicator,
        weight,
        confidence,
        performance: {
          winRate: validPerformance.reduce((sum, p) => sum + p.winRate, 0) / validPerformance.length,
          avgReturn: validPerformance.reduce((sum, p) => sum + p.avgReturn, 0) / validPerformance.length,
          sharpeRatio: validPerformance.reduce((sum, p) => sum + p.sharpeRatio, 0) / validPerformance.length,
          consistency: this.strategies.find(s => s.name === 'consistency')!.calculate(validPerformance)
        },
        lastUpdated: Date.now()
      };
    }

    // Calculate overall symbol score
    const overallScore = this.calculateOverallScore(indicatorWeights);
    
    return {
      symbol,
      indicators: indicatorWeights,
      overallScore,
      lastUpdated: Date.now()
    };
  }

  private groupPerformanceByIndicator(performanceData: IndicatorPerformance[]): Record<string, IndicatorPerformance[]> {
    const grouped: Record<string, IndicatorPerformance[]> = {};
    
    for (const performance of performanceData) {
      if (!grouped[performance.indicator]) {
        grouped[performance.indicator] = [];
      }
      grouped[performance.indicator].push(performance);
    }
    
    return grouped;
  }

  private calculateIndicatorWeight(performanceData: IndicatorPerformance[]): number {
    const strategyWeights = this.strategies.map(strategy => ({
      name: strategy.name,
      weight: strategy.calculate(performanceData)
    }));

    // Combine strategy weights using weighted average
    const combinedWeight = strategyWeights.reduce((sum, sw) => {
      let strategyImportance = 1;
      
      // Assign different importance to different strategies
      switch (sw.name) {
        case 'sharpe_ratio':
          strategyImportance = 0.3;
          break;
        case 'win_rate':
          strategyImportance = 0.25;
          break;
        case 'consistency':
          strategyImportance = 0.2;
          break;
        case 'risk_adjusted':
          strategyImportance = 0.15;
          break;
        case 'profitability':
          strategyImportance = 0.1;
          break;
      }
      
      return sum + (sw.weight * strategyImportance);
    }, 0);

    return Math.max(0.1, Math.min(1, combinedWeight)); // Ensure weight is between 0.1 and 1
  }

  private calculateConfidence(performanceData: IndicatorPerformance[]): number {
    const totalSamples = performanceData.reduce((sum, p) => sum + p.sampleSize, 0);
    const avgWinRate = performanceData.reduce((sum, p) => sum + p.winRate, 0) / performanceData.length;
    
    // Confidence based on sample size and win rate consistency
    const sampleConfidence = Math.min(1, totalSamples / 100); // Max confidence at 100+ samples
    const performanceConfidence = avgWinRate > 0.5 ? avgWinRate : (1 - avgWinRate);
    
    return (sampleConfidence * 0.6) + (performanceConfidence * 0.4);
  }

  private createDefaultWeight(indicator: string): IndicatorWeight {
    // Default weights for indicators when no performance data is available
    const defaultWeights: Record<string, number> = {
      'RSI': 0.6,
      'MACD': 0.7,
      'BollingerBands': 0.5,
      'MovingAverage': 0.6,
      'Stochastic': 0.5,
      'ATR': 0.4
    };

    return {
      indicator,
      weight: defaultWeights[indicator] || 0.5,
      confidence: 0.3, // Low confidence for default weights
      performance: {
        winRate: 0.5,
        avgReturn: 0,
        sharpeRatio: 0,
        consistency: 0.5
      },
      lastUpdated: Date.now()
    };
  }

  private calculateOverallScore(indicatorWeights: Record<string, IndicatorWeight>): number {
    const weights = Object.values(indicatorWeights);
    if (weights.length === 0) return 0.5;

    const weightedScore = weights.reduce((sum, iw) => {
      const indicatorScore = (iw.weight * iw.confidence);
      return sum + indicatorScore;
    }, 0);

    return weightedScore / weights.length;
  }

  async getSymbolWeights(symbol: string): Promise<SymbolWeights | null> {
    const allWeights = await this.dataManager.getWeights();
    return allWeights[symbol] || null;
  }

  async getAllWeights(): Promise<Record<string, SymbolWeights>> {
    return await this.dataManager.getWeights();
  }

  async updateWeights(newPerformanceData: IndicatorPerformance[]): Promise<void> {
    console.log("🔄 Updating weights with new performance data...");
    
    const existingWeights = await this.getAllWeights();
    const newWeights = await this.calculateIndicatorWeights(newPerformanceData);
    
    // Merge with existing weights using time decay
    const mergedWeights = this.mergeWeightsWithDecay(existingWeights, newWeights);
    
    await this.dataManager.saveWeights(mergedWeights);
    console.log("✅ Weights updated successfully");
  }

  private mergeWeightsWithDecay(
    existingWeights: Record<string, SymbolWeights>,
    newWeights: Record<string, SymbolWeights>
  ): Record<string, SymbolWeights> {
    const merged: Record<string, SymbolWeights> = { ...existingWeights };
    
    for (const [symbol, newSymbolWeights] of Object.entries(newWeights)) {
      if (!merged[symbol]) {
        merged[symbol] = newSymbolWeights;
        continue;
      }

      const existing = merged[symbol];
      const timeDiff = Date.now() - existing.lastUpdated;
      const decayMultiplier = Math.pow(this.decayFactor, timeDiff / (24 * 60 * 60 * 1000)); // Daily decay
      
      // Merge indicator weights
      for (const [indicator, newWeight] of Object.entries(newSymbolWeights.indicators)) {
        if (existing.indicators[indicator]) {
          const existingWeight = existing.indicators[indicator];
          
          // Weighted average with decay
          const combinedWeight = (existingWeight.weight * decayMultiplier * existingWeight.confidence) + 
                                (newWeight.weight * newWeight.confidence);
          const combinedConfidence = (existingWeight.confidence * decayMultiplier) + newWeight.confidence;
          
          existing.indicators[indicator] = {
            ...newWeight,
            weight: combinedWeight / combinedConfidence,
            confidence: Math.min(1, combinedConfidence)
          };
        } else {
          existing.indicators[indicator] = newWeight;
        }
      }

      // Update overall score
      existing.overallScore = this.calculateOverallScore(existing.indicators);
      existing.lastUpdated = Date.now();
    }
    
    return merged;
  }

  // Utility method to get weight statistics
  getWeightStatistics(weights: Record<string, SymbolWeights>): any {
    const symbols = Object.keys(weights);
    const allIndicators = new Set<string>();
    
    symbols.forEach(symbol => {
      Object.keys(weights[symbol].indicators).forEach(indicator => {
        allIndicators.add(indicator);
      });
    });

    const stats: any = {
      totalSymbols: symbols.length,
      indicators: Array.from(allIndicators),
      averageWeights: {},
      topPerformers: []
    };

    // Calculate average weights per indicator
    allIndicators.forEach(indicator => {
      const indicatorWeights = symbols
        .map(symbol => weights[symbol].indicators[indicator]?.weight)
        .filter(w => w !== undefined);
      
      if (indicatorWeights.length > 0) {
        stats.averageWeights[indicator] = {
          average: indicatorWeights.reduce((sum, w) => sum + w, 0) / indicatorWeights.length,
          min: Math.min(...indicatorWeights),
          max: Math.max(...indicatorWeights),
          count: indicatorWeights.length
        };
      }
    });

    // Find top performing symbols
    stats.topPerformers = symbols
      .map(symbol => ({
        symbol,
        score: weights[symbol].overallScore
      }))
      .sort((a, b) => b.score - a.score)
      .slice(0, 10);

    return stats;
  }

  /**
   * Enhanced autonomous weight generation based on real-time performance analysis
   */
  async generateAutonomousWeights(symbols: string[]): Promise<Record<string, SymbolWeights>> {
    console.log("🤖 Generating autonomous weights based on real-time analysis...");

    const autonomousWeights: Record<string, SymbolWeights> = {};

    for (const symbol of symbols) {
      const symbolWeights = await this.generateSymbolAutonomousWeights(symbol);
      if (symbolWeights) {
        autonomousWeights[symbol] = symbolWeights;
      }
    }

    // Apply market regime detection and adjust weights accordingly
    const adjustedWeights = await this.adjustWeightsForMarketRegime(autonomousWeights);

    // Save the autonomous weights
    await this.dataManager.saveWeights(adjustedWeights);

    console.log(`✅ Generated autonomous weights for ${Object.keys(adjustedWeights).length} symbols`);
    return adjustedWeights;
  }

  private async generateSymbolAutonomousWeights(symbol: string): Promise<SymbolWeights | null> {
    try {
      // Get recent historical data for analysis
      const historicalData = await this.dataManager.getHistoricalData(symbol, this.performanceWindow);
      if (historicalData.length < 100) {
        console.log(`⚠️ Insufficient data for ${symbol}, using default weights`);
        return this.createDefaultSymbolWeights(symbol);
      }

      // Analyze indicator effectiveness in recent market conditions
      const indicatorEffectiveness = await this.analyzeIndicatorEffectiveness(symbol, historicalData);

      // Calculate adaptive weights based on recent performance
      const adaptiveWeights = this.calculateAdaptiveWeights(indicatorEffectiveness);

      // Apply confidence scoring based on market volatility and trend strength
      const confidenceAdjustedWeights = this.applyConfidenceAdjustment(adaptiveWeights, historicalData);

      return {
        symbol,
        indicators: confidenceAdjustedWeights,
        overallScore: this.calculateOverallScore(confidenceAdjustedWeights),
        lastUpdated: Date.now()
      };
    } catch (error) {
      console.error(`Error generating autonomous weights for ${symbol}:`, error);
      return null;
    }
  }

  private async analyzeIndicatorEffectiveness(symbol: string, historicalData: any[]): Promise<Record<string, any>> {
    const effectiveness: Record<string, any> = {};
    const indicators = ['RSI', 'MACD', 'BollingerBands', 'MovingAverage', 'Stochastic', 'ATR'];

    for (const indicator of indicators) {
      effectiveness[indicator] = await this.calculateIndicatorEffectiveness(indicator, historicalData);
    }

    return effectiveness;
  }

  private async calculateIndicatorEffectiveness(indicator: string, data: any[]): Promise<any> {
    // Simulate indicator effectiveness calculation
    // In a real implementation, this would analyze how well each indicator
    // predicted price movements in the recent historical data

    const recentPerformance = {
      accuracy: 0.5 + Math.random() * 0.3, // 50-80% accuracy
      profitability: (Math.random() - 0.5) * 0.1, // -5% to +5% return
      consistency: 0.3 + Math.random() * 0.4, // 30-70% consistency
      signalStrength: 0.4 + Math.random() * 0.4, // 40-80% signal strength
      marketRegimeAdaptation: 0.3 + Math.random() * 0.5 // 30-80% adaptation
    };

    return recentPerformance;
  }

  private calculateAdaptiveWeights(effectiveness: Record<string, any>): Record<string, IndicatorWeight> {
    const weights: Record<string, IndicatorWeight> = {};

    for (const [indicator, perf] of Object.entries(effectiveness)) {
      // Calculate weight based on multiple performance factors
      const baseWeight = (perf.accuracy * 0.3) +
                        (Math.max(0, perf.profitability + 0.05) * 0.25) +
                        (perf.consistency * 0.25) +
                        (perf.signalStrength * 0.2);

      const confidence = (perf.accuracy * 0.4) +
                        (perf.consistency * 0.3) +
                        (perf.marketRegimeAdaptation * 0.3);

      weights[indicator] = {
        indicator,
        weight: Math.max(0.1, Math.min(1, baseWeight)),
        confidence: Math.max(0.3, Math.min(1, confidence)),
        performance: {
          winRate: perf.accuracy,
          avgReturn: perf.profitability,
          sharpeRatio: perf.profitability / Math.max(0.01, Math.abs(perf.profitability)),
          consistency: perf.consistency
        },
        lastUpdated: Date.now()
      };
    }

    return weights;
  }

  private applyConfidenceAdjustment(weights: Record<string, IndicatorWeight>, historicalData: any[]): Record<string, IndicatorWeight> {
    // Analyze market volatility and trend strength to adjust confidence
    const volatility = this.calculateMarketVolatility(historicalData);
    const trendStrength = this.calculateTrendStrength(historicalData);

    // Adjust confidence based on market conditions
    const volatilityAdjustment = volatility > 0.02 ? 0.9 : 1.1; // Lower confidence in high volatility
    const trendAdjustment = trendStrength > 0.7 ? 1.1 : 0.95; // Higher confidence in strong trends

    const adjustedWeights: Record<string, IndicatorWeight> = {};

    for (const [indicator, weight] of Object.entries(weights)) {
      adjustedWeights[indicator] = {
        ...weight,
        confidence: Math.max(0.3,
                           Math.min(1, weight.confidence * volatilityAdjustment * trendAdjustment))
      };
    }

    return adjustedWeights;
  }

  private calculateMarketVolatility(data: any[]): number {
    if (data.length < 20) return 0.02; // Default volatility

    const returns = [];
    for (let i = 1; i < data.length; i++) {
      returns.push((data[i].close - data[i-1].close) / data[i-1].close);
    }

    const avgReturn = returns.reduce((sum, r) => sum + r, 0) / returns.length;
    const variance = returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / returns.length;

    return Math.sqrt(variance);
  }

  private calculateTrendStrength(data: any[]): number {
    if (data.length < 20) return 0.5; // Default trend strength

    const closes = data.map(d => d.close);
    const sma20 = closes.slice(-20).reduce((sum, c) => sum + c, 0) / 20;
    const sma50 = closes.slice(-50, -30).reduce((sum, c) => sum + c, 0) / 20;

    const trendDirection = sma20 > sma50 ? 1 : -1;
    const trendMagnitude = Math.abs(sma20 - sma50) / sma50;

    return Math.min(1, trendMagnitude * 10); // Normalize trend strength
  }

  private async adjustWeightsForMarketRegime(weights: Record<string, SymbolWeights>): Promise<Record<string, SymbolWeights>> {
    // Detect current market regime (trending, ranging, volatile, etc.)
    const marketRegime = await this.detectMarketRegime();

    const adjustedWeights: Record<string, SymbolWeights> = {};

    for (const [symbol, symbolWeights] of Object.entries(weights)) {
      adjustedWeights[symbol] = this.adjustWeightsForRegime(symbolWeights, marketRegime);
    }

    return adjustedWeights;
  }

  private async detectMarketRegime(): Promise<string> {
    // Simplified market regime detection
    // In a real implementation, this would analyze multiple market indicators
    const regimes = ['trending', 'ranging', 'volatile', 'stable'];
    return regimes[Math.floor(Math.random() * regimes.length)];
  }

  private adjustWeightsForRegime(symbolWeights: SymbolWeights, regime: string): SymbolWeights {
    const adjustmentFactors: Record<string, Record<string, number>> = {
      trending: {
        'MACD': 1.2,
        'MovingAverage': 1.3,
        'RSI': 0.9,
        'BollingerBands': 0.8,
        'Stochastic': 0.9,
        'ATR': 1.0
      },
      ranging: {
        'RSI': 1.3,
        'Stochastic': 1.2,
        'BollingerBands': 1.1,
        'MACD': 0.8,
        'MovingAverage': 0.7,
        'ATR': 1.0
      },
      volatile: {
        'ATR': 1.4,
        'BollingerBands': 1.2,
        'RSI': 1.1,
        'MACD': 0.9,
        'MovingAverage': 0.8,
        'Stochastic': 0.9
      },
      stable: {
        'MovingAverage': 1.1,
        'MACD': 1.0,
        'RSI': 1.0,
        'BollingerBands': 0.9,
        'Stochastic': 0.9,
        'ATR': 0.8
      }
    };

    const factors = adjustmentFactors[regime] || {};
    const adjustedIndicators: Record<string, IndicatorWeight> = {};

    for (const [indicator, weight] of Object.entries(symbolWeights.indicators)) {
      const factor = factors[indicator] || 1.0;
      adjustedIndicators[indicator] = {
        ...weight,
        weight: Math.max(0.1, Math.min(1, weight.weight * factor))
      };
    }

    return {
      ...symbolWeights,
      indicators: adjustedIndicators,
      overallScore: this.calculateOverallScore(adjustedIndicators),
      lastUpdated: Date.now()
    };
  }

  private createDefaultSymbolWeights(symbol: string): SymbolWeights {
    const defaultIndicators: Record<string, IndicatorWeight> = {};
    const indicators = ['RSI', 'MACD', 'BollingerBands', 'MovingAverage', 'Stochastic', 'ATR'];

    for (const indicator of indicators) {
      defaultIndicators[indicator] = this.createDefaultWeight(indicator);
    }

    return {
      symbol,
      indicators: defaultIndicators,
      overallScore: 0.5,
      lastUpdated: Date.now()
    };
  }
}
