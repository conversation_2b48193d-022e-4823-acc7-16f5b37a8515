import { OHLCV } from "./technical-indicators.ts";
import { SymbolInfo } from "./market-scanner.ts";
import { config } from "./env.ts";
import { BybitWrapper } from "./bybit-wrapper.ts";

export interface HistoricalData {
  symbol: string;
  timeframe: string;
  data: OHLCV[];
  lastUpdate: number;
  metadata: {
    totalCandles: number;
    startTime: number;
    endTime: number;
  };
}

export interface CachedMarketData {
  symbols: SymbolInfo[];
  lastUpdate: number;
}

export interface DataCache {
  historical: Record<string, HistoricalData>;
  market: CachedMarketData;
  weights: Record<string, any>;
  correlations: Record<string, any>;
  probabilities: Record<string, any>;
}

export interface SymbolCache {
  symbol: string;
  klines: OHLCV[];
  indicators: Record<string, any>;
  correlations: Record<string, number>;
  weights: any;
  probabilities: any;
  lastUpdate: number;
  metadata: {
    totalCandles: number;
    startTime: number;
    endTime: number;
  };
}

export class DataManager {
  private client: BybitWrapper;
  private cache: DataCache;
  private readonly dataDir = "./data";
  private readonly cacheDir = "./data/cache";
  private readonly cacheFile = "./data/cache.json";
  private symbolCaches = new Map<string, SymbolCache>();
  private fetchedSymbols = new Set<string>();

  constructor() {
    this.client = new BybitWrapper();
    this.cache = {
      historical: {},
      market: { symbols: [], lastUpdate: 0 },
      weights: {},
      correlations: {},
      probabilities: {}
    };
  }

  async initialize(): Promise<void> {
    console.log("🔧 Initializing DataManager...");

    // Create data directories if they don't exist
    try {
      await Deno.mkdir(this.dataDir, { recursive: true });
      await Deno.mkdir(this.cacheDir, { recursive: true });
    } catch (error) {
      if (!(error instanceof Deno.errors.AlreadyExists)) {
        throw error;
      }
    }

    // Load existing cache
    await this.loadCache();

    // Load existing symbol caches
    await this.loadSymbolCaches();

    // Update market symbols if cache is stale
    if (this.isMarketCacheStale()) {
      await this.updateMarketSymbols();
    }

    console.log("✅ DataManager initialized");
  }

  private async loadCache(): Promise<void> {
    try {
      const cacheData = await Deno.readTextFile(this.cacheFile);
      this.cache = JSON.parse(cacheData);
      console.log("📂 Loaded existing cache");
    } catch (error) {
      console.log("📂 No existing cache found, starting fresh");
      await this.saveCache();
    }
  }

  private async saveCache(): Promise<void> {
    try {
      await Deno.writeTextFile(this.cacheFile, JSON.stringify(this.cache, null, 2));
    } catch (error) {
      console.error("❌ Error saving cache:", error);
    }
  }

  private async loadSymbolCaches(): Promise<void> {
    try {
      const cacheFiles = [];
      for await (const dirEntry of Deno.readDir(this.cacheDir)) {
        if (dirEntry.isFile && dirEntry.name.endsWith('.json')) {
          cacheFiles.push(dirEntry.name);
        }
      }

      for (const file of cacheFiles) {
        try {
          const symbol = file.replace('.json', '');
          const cacheData = await Deno.readTextFile(`${this.cacheDir}/${file}`);
          const symbolCache: SymbolCache = JSON.parse(cacheData);
          this.symbolCaches.set(symbol, symbolCache);
          this.fetchedSymbols.add(symbol);
        } catch (error) {
          console.warn(`⚠️ Error loading cache for ${file}:`, error);
        }
      }

      console.log(`📂 Loaded ${this.symbolCaches.size} symbol caches`);
    } catch (error) {
      console.log("📂 No existing symbol caches found");
    }
  }

  private async saveSymbolCache(symbol: string, symbolCache: SymbolCache): Promise<void> {
    try {
      const cacheFile = `${this.cacheDir}/${symbol}.json`;
      await Deno.writeTextFile(cacheFile, JSON.stringify(symbolCache, null, 2));
      this.symbolCaches.set(symbol, symbolCache);
    } catch (error) {
      console.error(`❌ Error saving symbol cache for ${symbol}:`, error);
    }
  }

  private isSymbolCacheStale(symbol: string): boolean {
    const symbolCache = this.symbolCaches.get(symbol);
    if (!symbolCache) return true;

    const now = Date.now();
    const cacheAge = now - symbolCache.lastUpdate;
    return cacheAge > (config.rebalanceHours * 60 * 60 * 1000);
  }

  private isMarketCacheStale(): boolean {
    const now = Date.now();
    const cacheAge = now - this.cache.market.lastUpdate;
    return cacheAge > (config.rebalanceHours * 60 * 60 * 1000);
  }

  private isHistoricalCacheStale(symbol: string): boolean {
    const historicalData = this.cache.historical[symbol];
    if (!historicalData) return true;
    
    const now = Date.now();
    const cacheAge = now - historicalData.lastUpdate;
    return cacheAge > (config.rebalanceHours * 60 * 60 * 1000);
  }

  async updateMarketSymbols(): Promise<void> {
    console.log("🔄 Updating market symbols...");

    try {
      const instruments = await this.client.getInstruments();

      this.cache.market.symbols = instruments.map(instrument => ({
        symbol: instrument.symbol,
        baseCoin: instrument.baseCoin,
        quoteCoin: instrument.quoteCoin,
        status: instrument.status,
        contractType: instrument.contractType || 'LinearPerpetual'
      }));

      this.cache.market.lastUpdate = Date.now();
      await this.saveCache();

      console.log(`✅ Updated ${this.cache.market.symbols.length} market symbols`);
    } catch (error) {
      console.error("❌ Error updating market symbols:", error);
      throw error;
    }
  }

  async getHistoricalData(symbol: string, daysBack: number = config.daysBack): Promise<OHLCV[]> {
    // Check if we already fetched data for this symbol in this session
    if (this.fetchedSymbols.has(symbol) && !this.isSymbolCacheStale(symbol)) {
      const symbolCache = this.symbolCaches.get(symbol);
      if (symbolCache?.klines && symbolCache.klines.length > 0) {
        console.log(`📊 Using cached data for ${symbol} (${symbolCache.klines.length} candles)`);
        return symbolCache.klines;
      }
    }

    console.log(`📈 Fetching historical data for ${symbol}...`);

    try {
      const endTime = Date.now();
      const startTime = endTime - (daysBack * 24 * 60 * 60 * 1000);
      const allCandles: OHLCV[] = [];

      let currentEndTime = endTime;

      // Fetch data in chunks
      while (currentEndTime > startTime && allCandles.length < (daysBack * 24)) {
        const klineData = await this.client.getKlines(symbol, config.timeframe, 1000, currentEndTime);

        if (!klineData || klineData.length === 0) {
          break;
        }

        const candles: OHLCV[] = klineData
          .map((kline: string[]) => ({
            timestamp: parseInt(kline[0]),
            open: parseFloat(kline[1]),
            high: parseFloat(kline[2]),
            low: parseFloat(kline[3]),
            close: parseFloat(kline[4]),
            volume: parseFloat(kline[5])
          }))
          .filter(candle => candle.timestamp >= startTime);

        allCandles.unshift(...candles.reverse());

        if (candles.length > 0) {
          currentEndTime = Math.min(...candles.map(c => c.timestamp)) - 1;
        } else {
          break;
        }
      }

      // Remove duplicates and sort by timestamp
      const uniqueCandles = Array.from(
        new Map(allCandles.map(candle => [candle.timestamp, candle])).values()
      ).sort((a, b) => a.timestamp - b.timestamp);

      // Create or update symbol cache
      const symbolCache: SymbolCache = {
        symbol,
        klines: uniqueCandles,
        indicators: {},
        correlations: {},
        weights: null,
        probabilities: null,
        lastUpdate: Date.now(),
        metadata: {
          totalCandles: uniqueCandles.length,
          startTime: uniqueCandles[0]?.timestamp || 0,
          endTime: uniqueCandles[uniqueCandles.length - 1]?.timestamp || 0
        }
      };

      // Save to sharded cache
      await this.saveSymbolCache(symbol, symbolCache);
      this.fetchedSymbols.add(symbol);

      console.log(`✅ Fetched ${uniqueCandles.length} candles for ${symbol}`);
      return uniqueCandles;

    } catch (error) {
      console.error(`❌ Error fetching historical data for ${symbol}:`, error);

      // Return cached data if available, even if stale
      const symbolCache = this.symbolCaches.get(symbol);
      if (symbolCache?.klines) {
        console.log(`📊 Falling back to cached data for ${symbol}`);
        return symbolCache.klines;
      }

      return [];
    }
  }

  async getAvailableSymbols(): Promise<SymbolInfo[]> {
    if (this.isMarketCacheStale()) {
      await this.updateMarketSymbols();
    }
    return this.cache.market.symbols;
  }

  async getTopSymbolsByVolume(limit: number = 50): Promise<SymbolInfo[]> {
    const symbols = await this.getAvailableSymbols();

    try {
      const tickers = await this.client.getTickers();

      // Sort by 24h volume and return top symbols
      const sortedTickers = tickers
        .filter(ticker => parseFloat(ticker.volume24h) > 0)
        .sort((a, b) => parseFloat(b.volume24h) - parseFloat(a.volume24h))
        .slice(0, limit);

      return sortedTickers
        .map(ticker => symbols.find(s => s.symbol === ticker.symbol))
        .filter(Boolean) as SymbolInfo[];
        
    } catch (error) {
      console.error("❌ Error getting top symbols by volume:", error);
      return symbols.slice(0, limit);
    }
  }

  // Enhanced methods for storing and retrieving analysis results with sharded caching
  async saveWeights(weights: Record<string, any>): Promise<void> {
    this.cache.weights = weights;
    await this.saveCache();

    // Also save to individual symbol caches
    for (const [symbol, symbolWeights] of Object.entries(weights)) {
      await this.saveSymbolWeights(symbol, symbolWeights);
    }
  }

  async getWeights(): Promise<Record<string, any>> {
    return this.cache.weights;
  }

  async saveSymbolWeights(symbol: string, weights: any): Promise<void> {
    const symbolCache = this.symbolCaches.get(symbol) || this.createEmptySymbolCache(symbol);
    symbolCache.weights = weights;
    symbolCache.lastUpdate = Date.now();
    await this.saveSymbolCache(symbol, symbolCache);
  }

  async getSymbolWeights(symbol: string): Promise<any> {
    const symbolCache = this.symbolCaches.get(symbol);
    return symbolCache?.weights || null;
  }

  async saveCorrelations(correlations: Record<string, any>): Promise<void> {
    this.cache.correlations = correlations;
    await this.saveCache();

    // Save correlation data to individual symbol caches
    if (correlations.matrix && correlations.symbols) {
      for (let i = 0; i < correlations.symbols.length; i++) {
        const symbol = correlations.symbols[i];
        const symbolCorrelations: Record<string, number> = {};

        for (let j = 0; j < correlations.symbols.length; j++) {
          if (i !== j) {
            symbolCorrelations[correlations.symbols[j]] = correlations.matrix[i][j];
          }
        }

        await this.saveSymbolCorrelations(symbol, symbolCorrelations);
      }
    }
  }

  async getCorrelations(): Promise<Record<string, any>> {
    return this.cache.correlations;
  }

  async saveSymbolCorrelations(symbol: string, correlations: Record<string, number>): Promise<void> {
    const symbolCache = this.symbolCaches.get(symbol) || this.createEmptySymbolCache(symbol);
    symbolCache.correlations = correlations;
    symbolCache.lastUpdate = Date.now();
    await this.saveSymbolCache(symbol, symbolCache);
  }

  async getSymbolCorrelations(symbol: string): Promise<Record<string, number>> {
    const symbolCache = this.symbolCaches.get(symbol);
    return symbolCache?.correlations || {};
  }

  async saveProbabilities(probabilities: Record<string, any>): Promise<void> {
    this.cache.probabilities = probabilities;
    await this.saveCache();

    // Save probability predictions to individual symbol caches
    if (probabilities.predictions) {
      for (const prediction of probabilities.predictions) {
        await this.saveSymbolProbabilities(prediction.symbol, prediction);
      }
    }
  }

  async getProbabilities(): Promise<Record<string, any>> {
    return this.cache.probabilities;
  }

  async saveSymbolProbabilities(symbol: string, probabilities: any): Promise<void> {
    const symbolCache = this.symbolCaches.get(symbol) || this.createEmptySymbolCache(symbol);
    symbolCache.probabilities = probabilities;
    symbolCache.lastUpdate = Date.now();
    await this.saveSymbolCache(symbol, symbolCache);
  }

  async getSymbolProbabilities(symbol: string): Promise<any> {
    const symbolCache = this.symbolCaches.get(symbol);
    return symbolCache?.probabilities || null;
  }

  private createEmptySymbolCache(symbol: string): SymbolCache {
    return {
      symbol,
      klines: [],
      indicators: {},
      correlations: {},
      weights: null,
      probabilities: null,
      lastUpdate: Date.now(),
      metadata: {
        totalCandles: 0,
        startTime: 0,
        endTime: 0
      }
    };
  }

  // Utility method to get cache statistics
  getCacheStats(): any {
    const historicalCount = Object.keys(this.cache.historical).length;
    const symbolCount = this.cache.market.symbols.length;
    const lastMarketUpdate = new Date(this.cache.market.lastUpdate).toISOString();
    
    return {
      historicalDatasets: historicalCount,
      availableSymbols: symbolCount,
      lastMarketUpdate,
      cacheSize: JSON.stringify(this.cache).length
    };
  }
}
