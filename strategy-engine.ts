import { TechnicalIndicators, RSIResult, <PERSON>DR<PERSON>ult, BollingerBandsResult } from "./technical-indicators.ts";
import { MarketData } from "./market-scanner.ts";

export interface TradingSignal {
  symbol: string;
  signal: 'LONG' | 'SHORT' | 'HOLD';
  confidence: number; // 0-100
  reason: string;
  indicators: {
    rsi?: number;
    macd?: { macd: number; signal: number; histogram: number };
    bollinger?: { position: string; price: number; upper: number; lower: number };
    stochastic?: { k: number; d: number };
    atr?: number;
    trend?: 'BULLISH' | 'BEARISH' | 'SIDEWAYS';
  };
  price: number;
  volume24h: number;
  priceChange24h: number;
  timestamp: number;
}

export interface StrategyConfig {
  rsiOversold: number;
  rsiOverbought: number;
  rsiPeriod: number;
  macdFast: number;
  macdSlow: number;
  macdSignal: number;
  bollingerPeriod: number;
  bollingerStdDev: number;
  stochasticK: number;
  stochasticD: number;
  atrPeriod: number;
  minVolume: number;
  minConfidence: number;
}

export class StrategyEngine {
  private config: StrategyConfig;

  constructor(
    private technicalIndicators: TechnicalIndicators,
    config?: Partial<StrategyConfig>
  ) {
    this.config = {
      rsiOversold: 30,
      rsiOverbought: 70,
      rsiPeriod: 14,
      macdFast: 12,
      macdSlow: 26,
      macdSignal: 9,
      bollingerPeriod: 20,
      bollingerStdDev: 2,
      stochasticK: 14,
      stochasticD: 3,
      atrPeriod: 14,
      minVolume: 1000000,
      minConfidence: 60,
      ...config
    };
  }

  /**
   * Analyze a symbol and generate trading signal
   */
  analyzeSymbol(marketData: MarketData): TradingSignal {
    const { symbol, price, volume24h, priceChange24h, klines } = marketData;
    
    if (klines.length < 50) {
      return this.createSignal(symbol, 'HOLD', 0, 'Insufficient data', {}, price, volume24h, priceChange24h);
    }

    const closes = klines.map(k => k.close);
    const highs = klines.map(k => k.high);
    const lows = klines.map(k => k.low);
    // const volumes = klines.map(k => k.volume); // Not used currently

    // Calculate technical indicators
    const rsi = this.technicalIndicators.rsi(closes, this.config.rsiPeriod);
    const macd = this.technicalIndicators.macd(closes, this.config.macdFast, this.config.macdSlow, this.config.macdSignal);
    const bollinger = this.technicalIndicators.bollingerBands(closes, this.config.bollingerPeriod, this.config.bollingerStdDev);
    const stochastic = this.technicalIndicators.stochastic(highs, lows, closes, this.config.stochasticK, this.config.stochasticD);
    const atr = this.technicalIndicators.atr(highs, lows, closes, this.config.atrPeriod);

    // Determine trend
    const sma20 = this.technicalIndicators.sma(closes, 20);
    const sma50 = this.technicalIndicators.sma(closes, 50);
    const trend = this.determineTrend(closes, sma20, sma50);

    // Analyze signals
    const analysis = this.analyzeIndicators(rsi, macd, bollinger, stochastic, trend, price);
    
    const indicators = {
      rsi: rsi?.value,
      macd: macd ? { macd: macd.macd, signal: macd.signal, histogram: macd.histogram } : undefined,
      bollinger: bollinger ? {
        position: bollinger.position,
        price,
        upper: bollinger.upper,
        lower: bollinger.lower
      } : undefined,
      stochastic: stochastic ? { k: stochastic.k, d: stochastic.d } : undefined,
      atr: atr ?? undefined, // Convert null to undefined
      trend
    };

    return this.createSignal(
      symbol,
      analysis.signal,
      analysis.confidence,
      analysis.reason,
      indicators,
      price,
      volume24h,
      priceChange24h
    );
  }

  /**
   * Analyze all indicators and generate signal
   */
  private analyzeIndicators(
    rsi: RSIResult | null,
    macd: MACDResult | null,
    bollinger: BollingerBandsResult | null,
    stochastic: { k: number; d: number; signal: 'BUY' | 'SELL' | 'NEUTRAL' } | null,
    trend: 'BULLISH' | 'BEARISH' | 'SIDEWAYS',
    _currentPrice: number // Prefixed with underscore since it's not used
  ): { signal: 'LONG' | 'SHORT' | 'HOLD'; confidence: number; reason: string } {
    const signals: Array<{ type: 'LONG' | 'SHORT'; strength: number; reason: string }> = [];

    // RSI Analysis
    if (rsi) {
      if (rsi.oversold) {
        signals.push({ type: 'LONG', strength: 25, reason: 'RSI oversold' });
      } else if (rsi.overbought) {
        signals.push({ type: 'SHORT', strength: 25, reason: 'RSI overbought' });
      }
    }

    // MACD Analysis
    if (macd) {
      if (macd.bullish) {
        signals.push({ type: 'LONG', strength: 30, reason: 'MACD bullish crossover' });
      } else if (macd.bearish) {
        signals.push({ type: 'SHORT', strength: 30, reason: 'MACD bearish crossover' });
      } else if (macd.histogram > 0 && macd.macd > macd.signal) {
        signals.push({ type: 'LONG', strength: 15, reason: 'MACD above signal' });
      } else if (macd.histogram < 0 && macd.macd < macd.signal) {
        signals.push({ type: 'SHORT', strength: 15, reason: 'MACD below signal' });
      }
    }

    // Bollinger Bands Analysis
    if (bollinger) {
      if (bollinger.position === 'BELOW_LOWER') {
        signals.push({ type: 'LONG', strength: 20, reason: 'Price below lower Bollinger Band' });
      } else if (bollinger.position === 'ABOVE_UPPER') {
        signals.push({ type: 'SHORT', strength: 20, reason: 'Price above upper Bollinger Band' });
      }
    }

    // Stochastic Analysis
    if (stochastic) {
      if (stochastic.signal === 'BUY') {
        signals.push({ type: 'LONG', strength: 15, reason: 'Stochastic oversold' });
      } else if (stochastic.signal === 'SELL') {
        signals.push({ type: 'SHORT', strength: 15, reason: 'Stochastic overbought' });
      }
    }

    // Trend Analysis
    if (trend === 'BULLISH') {
      signals.push({ type: 'LONG', strength: 20, reason: 'Bullish trend' });
    } else if (trend === 'BEARISH') {
      signals.push({ type: 'SHORT', strength: 20, reason: 'Bearish trend' });
    }

    // Calculate final signal
    const longStrength = signals.filter(s => s.type === 'LONG').reduce((sum, s) => sum + s.strength, 0);
    const shortStrength = signals.filter(s => s.type === 'SHORT').reduce((sum, s) => sum + s.strength, 0);

    const netStrength = longStrength - shortStrength;
    const confidence = Math.min(Math.abs(netStrength), 100);

    let finalSignal: 'LONG' | 'SHORT' | 'HOLD' = 'HOLD';
    let reason = 'No clear signal';

    if (confidence >= this.config.minConfidence) {
      if (netStrength > 0) {
        finalSignal = 'LONG';
        const longReasons = signals.filter(s => s.type === 'LONG').map(s => s.reason);
        reason = `Bullish: ${longReasons.join(', ')}`;
      } else {
        finalSignal = 'SHORT';
        const shortReasons = signals.filter(s => s.type === 'SHORT').map(s => s.reason);
        reason = `Bearish: ${shortReasons.join(', ')}`;
      }
    } else {
      reason = `Weak signal (${confidence}% confidence)`;
    }

    return { signal: finalSignal, confidence, reason };
  }

  /**
   * Determine overall trend
   */
  private determineTrend(
    closes: number[],
    sma20: number[],
    sma50: number[]
  ): 'BULLISH' | 'BEARISH' | 'SIDEWAYS' {
    if (sma20.length === 0 || sma50.length === 0) return 'SIDEWAYS';

    const currentPrice = closes[closes.length - 1];
    const currentSMA20 = sma20[sma20.length - 1];
    const currentSMA50 = sma50[sma50.length - 1];

    // Price above both SMAs and SMA20 > SMA50 = Bullish
    if (currentPrice > currentSMA20 && currentSMA20 > currentSMA50) {
      return 'BULLISH';
    }
    
    // Price below both SMAs and SMA20 < SMA50 = Bearish
    if (currentPrice < currentSMA20 && currentSMA20 < currentSMA50) {
      return 'BEARISH';
    }

    return 'SIDEWAYS';
  }

  /**
   * Create a trading signal object
   */
  private createSignal(
    symbol: string,
    signal: 'LONG' | 'SHORT' | 'HOLD',
    confidence: number,
    reason: string,
    indicators: TradingSignal['indicators'],
    price: number,
    volume24h: number,
    priceChange24h: number
  ): TradingSignal {
    return {
      symbol,
      signal,
      confidence: Math.round(confidence),
      reason,
      indicators,
      price,
      volume24h,
      priceChange24h,
      timestamp: Date.now()
    };
  }

  /**
   * Filter signals by minimum confidence
   */
  filterSignals(signals: TradingSignal[], minConfidence?: number): TradingSignal[] {
    const threshold = minConfidence ?? this.config.minConfidence;
    return signals.filter(signal => 
      signal.confidence >= threshold && signal.signal !== 'HOLD'
    );
  }

  /**
   * Get strategy configuration
   */
  getConfig(): StrategyConfig {
    return { ...this.config };
  }

  /**
   * Update strategy configuration
   */
  updateConfig(newConfig: Partial<StrategyConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }
}
