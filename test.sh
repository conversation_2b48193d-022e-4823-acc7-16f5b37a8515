#!/bin/bash

echo "🧪 Starting 60-second trading system test..."
echo "⏰ Test will automatically stop after 60 seconds"
echo ""

# Start the trading system in background
deno run -A main.ts &
DENO_PID=$!

# Wait for 60 seconds
sleep 60

# Kill the process
kill $DENO_PID 2>/dev/null || true

echo ""
echo "✅ Test completed after 60 seconds"
echo "📊 Check the ./data/signals/ directory for generated signal files"
echo "💾 Check the logs above to see if trades were executed"
