[{"timestamp": *************, "method": "getWalletBalance", "params": {"accountType": "UNIFIED"}, "success": true, "duration": 857, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 1, "hasResult": true, "orderId": null, "listLength": 1}, "rateLimitInfo": {"remainingRequests": 49, "maxRequests": 50, "resetAtTimestamp": *************}}, {"timestamp": *************, "method": "getPositionInfo", "params": {"category": "linear", "settleCoin": "USDT"}, "success": true, "duration": 614, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 3, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 49, "maxRequests": 50, "resetAtTimestamp": *************}}, {"timestamp": *************, "method": "getWalletBalance", "params": {"accountType": "UNIFIED"}, "success": true, "duration": 611, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 1, "hasResult": true, "orderId": null, "listLength": 1}, "rateLimitInfo": {"remainingRequests": 49, "maxRequests": 50, "resetAtTimestamp": *************}}, {"timestamp": *************, "method": "getPositionInfo", "params": {"category": "linear", "settleCoin": "USDT"}, "success": true, "duration": 235, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 3, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 49, "maxRequests": 50, "resetAtTimestamp": *************}}, {"timestamp": *************, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 1051, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}}, {"timestamp": *************, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 1456, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}}, {"timestamp": 1749830566651, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 1562, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}, "rateLimitInfo": {"remainingRequests": 49, "maxRequests": 50, "resetAtTimestamp": *************}}, {"timestamp": 1749830568214, "method": "setLeverage", "params": {"category": "linear", "symbol": "ZEREBROUSDT", "buyLeverage": "2", "sellLeverage": "2"}, "success": false, "duration": 286, "error": {"message": "leverage not modified", "retCode": 110043, "retMsg": "leverage not modified"}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830568389}}, {"timestamp": 1749830568507, "method": "submitOrder", "params": {"category": "linear", "symbol": "ZEREBROUSDT", "side": "<PERSON>ll", "orderType": "Market", "qty": "5918.3", "timeInForce": "IOC"}, "success": false, "duration": 817, "error": {"message": "Qty invalid", "retCode": 10001, "retMsg": "Qty invalid"}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830569221}}, {"timestamp": 1749830569328, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 485, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830569221}}, {"timestamp": 1749830569813, "method": "setLeverage", "params": {"category": "linear", "symbol": "MYRIAUSDT", "buyLeverage": "2", "sellLeverage": "2"}, "success": false, "duration": 236, "error": {"message": "leverage not modified", "retCode": 110043, "retMsg": "leverage not modified"}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830569947}}, {"timestamp": 1749830570051, "method": "submitOrder", "params": {"category": "linear", "symbol": "MYRIAUSDT", "side": "Buy", "orderType": "Market", "qty": "93260", "timeInForce": "IOC"}, "success": true, "duration": 250, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": "565a1753-0445-4ab9-9520-484fa57267ce", "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830570199}}, {"timestamp": 1749830571306, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 1029, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830570199}}, {"timestamp": 1749830572335, "method": "setLeverage", "params": {"category": "linear", "symbol": "ANIMEUSDT", "buyLeverage": "2", "sellLeverage": "2"}, "success": false, "duration": 236, "error": {"message": "leverage not modified", "retCode": 110043, "retMsg": "leverage not modified"}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830572469}}, {"timestamp": 1749830572572, "method": "submitOrder", "params": {"category": "linear", "symbol": "ANIMEUSDT", "side": "Buy", "orderType": "Market", "qty": "5414.8", "timeInForce": "IOC"}, "success": false, "duration": 247, "error": {"message": "Qty invalid", "retCode": 10001, "retMsg": "Qty invalid"}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830572717}}, {"timestamp": 1749830572820, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 265, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830572717}}, {"timestamp": 1749830573085, "method": "setLeverage", "params": {"category": "linear", "symbol": "HIPPOUSDT", "buyLeverage": "2", "sellLeverage": "2"}, "success": true, "duration": 240, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830573217}}, {"timestamp": 1749830573325, "method": "submitOrder", "params": {"category": "linear", "symbol": "HIPPOUSDT", "side": "Buy", "orderType": "Market", "qty": "44973", "timeInForce": "IOC"}, "success": true, "duration": 246, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": "92795030-fbdf-4322-8912-03ffe5083e43", "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830573469}}, {"timestamp": 1749830574574, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 278, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830573469}}, {"timestamp": 1749830574852, "method": "setLeverage", "params": {"category": "linear", "symbol": "RVNUSDT", "buyLeverage": "2", "sellLeverage": "2"}, "success": false, "duration": 254, "error": {"message": "leverage not modified", "retCode": 110043, "retMsg": "leverage not modified"}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830575003}}, {"timestamp": 1749830575111, "method": "submitOrder", "params": {"category": "linear", "symbol": "RVNUSDT", "side": "<PERSON>ll", "orderType": "Market", "qty": "4373.9", "timeInForce": "IOC"}, "success": false, "duration": 329, "error": {"message": "Qty invalid", "retCode": 10001, "retMsg": "Qty invalid"}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830575251}}, {"timestamp": 1749830575509, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 294, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830575251}}, {"timestamp": 1749830575804, "method": "setLeverage", "params": {"category": "linear", "symbol": "SPELLUSDT", "buyLeverage": "2", "sellLeverage": "2"}, "success": false, "duration": 247, "error": {"message": "leverage not modified", "retCode": 110043, "retMsg": "leverage not modified"}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830575947}}, {"timestamp": 1749830576053, "method": "submitOrder", "params": {"category": "linear", "symbol": "SPELLUSDT", "side": "<PERSON>ll", "orderType": "Market", "qty": "172185", "timeInForce": "IOC"}, "success": true, "duration": 242, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": "1f93f912-d28d-42bc-b0ff-671c4c5829b6", "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830576189}}, {"timestamp": 1749830577299, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 276, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830576189}}, {"timestamp": 1749830577576, "method": "setLeverage", "params": {"category": "linear", "symbol": "ZBCNUSDT", "buyLeverage": "2", "sellLeverage": "2"}, "success": false, "duration": 234, "error": {"message": "leverage not modified", "retCode": 110043, "retMsg": "leverage not modified"}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830577709}}, {"timestamp": 1749830577811, "method": "submitOrder", "params": {"category": "linear", "symbol": "ZBCNUSDT", "side": "Buy", "orderType": "Market", "qty": "13681", "timeInForce": "IOC"}, "success": true, "duration": 241, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": "fbbb84df-c885-4943-a49b-087061573afb", "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830577950}}, {"timestamp": 1749830579068, "method": "getTickers", "params": {"category": "linear"}, "success": true, "duration": 463, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": null, "listLength": 591}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830577950}}, {"timestamp": 1749830579532, "method": "setLeverage", "params": {"category": "linear", "symbol": "DOGSUSDT", "buyLeverage": "2", "sellLeverage": "2"}, "success": true, "duration": 290, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 0, "hasResult": true, "orderId": null, "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830579719}}, {"timestamp": 1749830579827, "method": "submitOrder", "params": {"category": "linear", "symbol": "DOGSUSDT", "side": "<PERSON>ll", "orderType": "Market", "qty": "495801", "timeInForce": "IOC"}, "success": true, "duration": 254, "response": {"retCode": 0, "retMsg": "OK", "resultSize": 2, "hasResult": true, "orderId": "c8852389-38c1-49ca-9e29-b726489a21f9", "listLength": 0}, "rateLimitInfo": {"remainingRequests": 9, "maxRequests": 10, "resetAtTimestamp": 1749830579979}}]