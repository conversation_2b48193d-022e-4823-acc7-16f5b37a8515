import { RateLimitInfo } from "./types.ts";

export interface RateLimitConfig {
  requestsPerSecond: number;
  requestsPerMinute: number;
  burstLimit: number;
  useAPIRateLimit: boolean; // Use rate limit info from API responses
}

export class RateLimiter {
  private requestTimes: number[] = [];
  private config: RateLimitConfig;
  private lastRateLimitInfo: RateLimitInfo | null = null;

  constructor(config?: Partial<RateLimitConfig>) {
    // Bybit rate limits: 120 requests per minute for most endpoints
    this.config = {
      requestsPerSecond: 10,
      requestsPerMinute: 100, // Conservative limit
      burstLimit: 5,
      useAPIRateLimit: true, // Use API rate limit info when available
      ...config
    };
  }

  /**
   * Update rate limit info from API response
   */
  updateRateLimitInfo(rateLimitInfo: RateLimitInfo): void {
    this.lastRateLimitInfo = rateLimitInfo;
  }

  /**
   * Wait if necessary to respect rate limits
   */
  async waitIfNeeded(): Promise<void> {
    const now = Date.now();

    // Use API rate limit info if available and enabled
    if (this.config.useAPIRateLimit && this.lastRateLimitInfo) {
      const { remainingRequests, resetAtTimestamp } = this.lastRateLimitInfo;

      // If we're close to the limit, wait
      if (remainingRequests <= 2) {
        const waitTime = Math.max(0, resetAtTimestamp - now + 100); // Add 100ms buffer
        if (waitTime > 0) {
          console.log(`⏳ API rate limit reached (${remainingRequests} remaining), waiting ${waitTime}ms...`);
          await this.sleep(waitTime);
          return;
        }
      }

      // If we have plenty of requests remaining, just add a small delay
      if (remainingRequests > 10) {
        await this.sleep(50); // Small delay to be conservative
        this.requestTimes.push(now);
        return;
      }
    }

    // Fallback to manual rate limiting
    // Clean old requests (older than 1 minute)
    this.requestTimes = this.requestTimes.filter(time => now - time < 60000);

    // Check per-minute limit
    if (this.requestTimes.length >= this.config.requestsPerMinute) {
      const oldestRequest = Math.min(...this.requestTimes);
      const waitTime = 60000 - (now - oldestRequest) + 100; // Add 100ms buffer
      if (waitTime > 0) {
        console.log(`⏳ Rate limit reached, waiting ${waitTime}ms...`);
        await this.sleep(waitTime);
      }
    }

    // Check per-second limit (last 1 second)
    const recentRequests = this.requestTimes.filter(time => now - time < 1000);
    if (recentRequests.length >= this.config.requestsPerSecond) {
      const waitTime = 1000 - (now - Math.min(...recentRequests)) + 50; // Add 50ms buffer
      if (waitTime > 0) {
        await this.sleep(waitTime);
      }
    }

    // Check burst limit (last 100ms)
    const burstRequests = this.requestTimes.filter(time => now - time < 100);
    if (burstRequests.length >= this.config.burstLimit) {
      await this.sleep(100);
    }

    // Record this request
    this.requestTimes.push(Date.now());
  }

  /**
   * Execute a function with rate limiting and update rate limit info
   */
  async execute<T>(fn: () => Promise<T>): Promise<T> {
    await this.waitIfNeeded();
    const result = await fn();

    // Extract rate limit info if available
    if (this.config.useAPIRateLimit && result && typeof result === 'object' && 'rateLimitApi' in result) {
      const apiResponse = result as { rateLimitApi: RateLimitInfo };
      this.updateRateLimitInfo(apiResponse.rateLimitApi);
    }

    return result;
  }

  /**
   * Execute multiple functions with rate limiting and optional concurrency control
   */
  async executeMany<T>(
    functions: (() => Promise<T>)[],
    options: { concurrency?: number; onProgress?: (completed: number, total: number) => void } = {}
  ): Promise<T[]> {
    const { concurrency = 3, onProgress } = options;
    const results: T[] = [];
    
    // Process in batches to control concurrency
    for (let i = 0; i < functions.length; i += concurrency) {
      const batch = functions.slice(i, i + concurrency);
      const batchPromises = batch.map(fn => this.execute(fn));
      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
      
      if (onProgress) {
        onProgress(results.length, functions.length);
      }
      
      // Small delay between batches
      if (i + concurrency < functions.length) {
        await this.sleep(200);
      }
    }
    
    return results;
  }

  /**
   * Get current rate limit status
   */
  getStatus(): {
    requestsInLastMinute: number;
    requestsInLastSecond: number;
    canMakeRequest: boolean;
  } {
    const now = Date.now();
    const requestsInLastMinute = this.requestTimes.filter(time => now - time < 60000).length;
    const requestsInLastSecond = this.requestTimes.filter(time => now - time < 1000).length;
    
    return {
      requestsInLastMinute,
      requestsInLastSecond,
      canMakeRequest: requestsInLastMinute < this.config.requestsPerMinute && 
                     requestsInLastSecond < this.config.requestsPerSecond
    };
  }

  /**
   * Reset rate limiter
   */
  reset(): void {
    this.requestTimes = [];
  }

  private sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
