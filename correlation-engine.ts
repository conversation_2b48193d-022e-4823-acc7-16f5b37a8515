import { OHLCV } from "./technical-indicators.ts";
import { DataManager } from "./data-manager.ts";

export interface CorrelationMatrix {
  symbols: string[];
  matrix: number[][];
  timestamp: number;
  timeframe: string;
  period: number; // days of data used
}

export interface SymbolCorrelation {
  symbol1: string;
  symbol2: string;
  correlation: number;
  strength: 'VERY_STRONG' | 'STRONG' | 'MODERATE' | 'WEAK' | 'VERY_WEAK';
  direction: 'POSITIVE' | 'NEGATIVE';
}

export interface MarketCluster {
  id: string;
  symbols: string[];
  avgCorrelation: number;
  description: string;
  representative: string; // Most central symbol in cluster
}

export interface PortfolioBalance {
  longPositions: string[];
  shortPositions: string[];
  neutralScore: number; // How market-neutral the portfolio is (0-1)
  riskScore: number; // Overall portfolio risk (0-1)
  diversificationScore: number; // How diversified the portfolio is (0-1)
}

export class CorrelationEngine {
  private dataManager: DataManager;
  private readonly correlationThresholds = {
    veryStrong: 0.8,
    strong: 0.6,
    moderate: 0.4,
    weak: 0.2
  };

  constructor(dataManager: DataManager) {
    this.dataManager = dataManager;
  }

  async calculateCorrelationMatrix(symbols: string[], daysBack: number = 30): Promise<CorrelationMatrix> {
    console.log("🔗 Calculating correlation matrix...");
    
    // Fetch historical data for all symbols
    const historicalData: Record<string, OHLCV[]> = {};
    const validSymbols: string[] = [];
    
    for (const symbol of symbols) {
      const data = await this.dataManager.getHistoricalData(symbol, daysBack);
      if (data.length >= 100) { // Minimum data requirement
        historicalData[symbol] = data;
        validSymbols.push(symbol);
      }
    }

    console.log(`📊 Calculating correlations for ${validSymbols.length} symbols...`);
    
    // Calculate correlation matrix
    const matrix: number[][] = [];
    
    for (let i = 0; i < validSymbols.length; i++) {
      matrix[i] = [];
      for (let j = 0; j < validSymbols.length; j++) {
        if (i === j) {
          matrix[i][j] = 1.0; // Perfect correlation with itself
        } else {
          const correlation = this.calculatePearsonCorrelation(
            historicalData[validSymbols[i]],
            historicalData[validSymbols[j]]
          );
          matrix[i][j] = correlation;
        }
      }
    }

    const correlationMatrix: CorrelationMatrix = {
      symbols: validSymbols,
      matrix,
      timestamp: Date.now(),
      timeframe: '1h',
      period: daysBack
    };

    // Save to cache
    await this.dataManager.saveCorrelations({ matrix: correlationMatrix });
    
    console.log("✅ Correlation matrix calculated and cached");
    return correlationMatrix;
  }

  private calculatePearsonCorrelation(data1: OHLCV[], data2: OHLCV[]): number {
    // Align data by timestamp and calculate returns
    const returns1 = this.calculateReturns(data1);
    const returns2 = this.calculateReturns(data2);
    
    // Find common timestamps
    const commonData = this.alignDataByTimestamp(returns1, returns2);
    
    if (commonData.length < 10) return 0; // Insufficient data
    
    const values1 = commonData.map(d => d.return1);
    const values2 = commonData.map(d => d.return2);
    
    return this.pearsonCorrelation(values1, values2);
  }

  private calculateReturns(data: OHLCV[]): Array<{timestamp: number, return: number}> {
    const returns: Array<{timestamp: number, return: number}> = [];
    
    for (let i = 1; i < data.length; i++) {
      const returnValue = (data[i].close - data[i-1].close) / data[i-1].close;
      returns.push({
        timestamp: data[i].timestamp,
        return: returnValue
      });
    }
    
    return returns;
  }

  private alignDataByTimestamp(
    returns1: Array<{timestamp: number, return: number}>,
    returns2: Array<{timestamp: number, return: number}>
  ): Array<{timestamp: number, return1: number, return2: number}> {
    const aligned: Array<{timestamp: number, return1: number, return2: number}> = [];
    
    const map1 = new Map(returns1.map(r => [r.timestamp, r.return]));
    const map2 = new Map(returns2.map(r => [r.timestamp, r.return]));
    
    for (const [timestamp, return1] of map1) {
      const return2 = map2.get(timestamp);
      if (return2 !== undefined) {
        aligned.push({ timestamp, return1, return2 });
      }
    }
    
    return aligned;
  }

  private pearsonCorrelation(x: number[], y: number[]): number {
    const n = x.length;
    if (n !== y.length || n === 0) return 0;
    
    const sumX = x.reduce((sum, val) => sum + val, 0);
    const sumY = y.reduce((sum, val) => sum + val, 0);
    const sumXY = x.reduce((sum, val, i) => sum + val * y[i], 0);
    const sumX2 = x.reduce((sum, val) => sum + val * val, 0);
    const sumY2 = y.reduce((sum, val) => sum + val * val, 0);
    
    const numerator = n * sumXY - sumX * sumY;
    const denominator = Math.sqrt((n * sumX2 - sumX * sumX) * (n * sumY2 - sumY * sumY));
    
    return denominator === 0 ? 0 : numerator / denominator;
  }

  async getSymbolCorrelations(targetSymbol: string, correlationMatrix?: CorrelationMatrix): Promise<SymbolCorrelation[]> {
    let matrix = correlationMatrix;
    
    if (!matrix) {
      const cached = await this.dataManager.getCorrelations();
      matrix = cached.matrix;
    }
    
    if (!matrix) {
      throw new Error("No correlation matrix available");
    }
    
    const targetIndex = matrix.symbols.indexOf(targetSymbol);
    if (targetIndex === -1) {
      throw new Error(`Symbol ${targetSymbol} not found in correlation matrix`);
    }
    
    const correlations: SymbolCorrelation[] = [];
    
    for (let i = 0; i < matrix.symbols.length; i++) {
      if (i === targetIndex) continue;
      
      const correlation = matrix.matrix[targetIndex][i];
      const absCorrelation = Math.abs(correlation);
      
      let strength: SymbolCorrelation['strength'];
      if (absCorrelation >= this.correlationThresholds.veryStrong) {
        strength = 'VERY_STRONG';
      } else if (absCorrelation >= this.correlationThresholds.strong) {
        strength = 'STRONG';
      } else if (absCorrelation >= this.correlationThresholds.moderate) {
        strength = 'MODERATE';
      } else if (absCorrelation >= this.correlationThresholds.weak) {
        strength = 'WEAK';
      } else {
        strength = 'VERY_WEAK';
      }
      
      correlations.push({
        symbol1: targetSymbol,
        symbol2: matrix.symbols[i],
        correlation,
        strength,
        direction: correlation >= 0 ? 'POSITIVE' : 'NEGATIVE'
      });
    }
    
    return correlations.sort((a, b) => Math.abs(b.correlation) - Math.abs(a.correlation));
  }

  async identifyMarketClusters(correlationMatrix?: CorrelationMatrix): Promise<MarketCluster[]> {
    let matrix = correlationMatrix;
    
    if (!matrix) {
      const cached = await this.dataManager.getCorrelations();
      matrix = cached.matrix;
    }
    
    if (!matrix) {
      throw new Error("No correlation matrix available");
    }
    
    console.log("🎯 Identifying market clusters...");
    
    const clusters: MarketCluster[] = [];
    const visited = new Set<number>();
    
    for (let i = 0; i < matrix.symbols.length; i++) {
      if (visited.has(i)) continue;
      
      const cluster = this.findCluster(matrix, i, visited);
      if (cluster.symbols.length >= 2) { // Minimum cluster size
        clusters.push(cluster);
      }
    }
    
    console.log(`✅ Identified ${clusters.length} market clusters`);
    return clusters;
  }

  private findCluster(matrix: CorrelationMatrix, startIndex: number, visited: Set<number>): MarketCluster {
    const clusterIndices = [startIndex];
    const queue = [startIndex];
    visited.add(startIndex);
    
    while (queue.length > 0) {
      const currentIndex = queue.shift()!;
      
      for (let i = 0; i < matrix.symbols.length; i++) {
        if (visited.has(i)) continue;
        
        const correlation = Math.abs(matrix.matrix[currentIndex][i]);
        if (correlation >= this.correlationThresholds.strong) {
          visited.add(i);
          clusterIndices.push(i);
          queue.push(i);
        }
      }
    }
    
    const symbols = clusterIndices.map(i => matrix.symbols[i]);
    const avgCorrelation = this.calculateAverageCorrelation(matrix, clusterIndices);
    const representative = this.findMostCentralSymbol(matrix, clusterIndices);
    
    return {
      id: `cluster_${symbols.join('_').substring(0, 20)}`,
      symbols,
      avgCorrelation,
      description: `Market cluster with ${symbols.length} symbols (avg correlation: ${avgCorrelation.toFixed(3)})`,
      representative: matrix.symbols[representative]
    };
  }

  private calculateAverageCorrelation(matrix: CorrelationMatrix, indices: number[]): number {
    let sum = 0;
    let count = 0;
    
    for (let i = 0; i < indices.length; i++) {
      for (let j = i + 1; j < indices.length; j++) {
        sum += Math.abs(matrix.matrix[indices[i]][indices[j]]);
        count++;
      }
    }
    
    return count > 0 ? sum / count : 0;
  }

  private findMostCentralSymbol(matrix: CorrelationMatrix, indices: number[]): number {
    let maxCentrality = -1;
    let mostCentral = indices[0];
    
    for (const index of indices) {
      let centrality = 0;
      for (const otherIndex of indices) {
        if (index !== otherIndex) {
          centrality += Math.abs(matrix.matrix[index][otherIndex]);
        }
      }
      
      if (centrality > maxCentrality) {
        maxCentrality = centrality;
        mostCentral = index;
      }
    }
    
    return mostCentral;
  }

  async optimizePortfolioBalance(
    longCandidates: string[],
    shortCandidates: string[],
    maxPositions: number = 10
  ): Promise<PortfolioBalance> {
    console.log("⚖️ Optimizing portfolio balance...");
    
    const correlationMatrix = await this.getCorrelationMatrix();
    if (!correlationMatrix) {
      throw new Error("Correlation matrix not available");
    }
    
    // Select positions to minimize correlation
    const selectedLongs = this.selectUncorrelatedPositions(longCandidates, correlationMatrix, Math.floor(maxPositions / 2));
    const selectedShorts = this.selectUncorrelatedPositions(shortCandidates, correlationMatrix, Math.ceil(maxPositions / 2));
    
    // Calculate portfolio metrics
    const neutralScore = this.calculateNeutralityScore(selectedLongs, selectedShorts, correlationMatrix);
    const riskScore = this.calculateRiskScore(selectedLongs.concat(selectedShorts), correlationMatrix);
    const diversificationScore = this.calculateDiversificationScore(selectedLongs.concat(selectedShorts), correlationMatrix);
    
    return {
      longPositions: selectedLongs,
      shortPositions: selectedShorts,
      neutralScore,
      riskScore,
      diversificationScore
    };
  }

  private selectUncorrelatedPositions(candidates: string[], matrix: CorrelationMatrix, maxCount: number): string[] {
    if (candidates.length <= maxCount) return candidates;
    
    const selected: string[] = [];
    const remaining = [...candidates];
    
    // Start with a random candidate
    const firstIndex = Math.floor(Math.random() * remaining.length);
    selected.push(remaining.splice(firstIndex, 1)[0]);
    
    while (selected.length < maxCount && remaining.length > 0) {
      let bestCandidate = '';
      let lowestMaxCorrelation = 1;
      
      for (const candidate of remaining) {
        let maxCorrelation = 0;
        
        for (const selectedSymbol of selected) {
          const correlation = this.getCorrelationBetweenSymbols(candidate, selectedSymbol, matrix);
          maxCorrelation = Math.max(maxCorrelation, Math.abs(correlation));
        }
        
        if (maxCorrelation < lowestMaxCorrelation) {
          lowestMaxCorrelation = maxCorrelation;
          bestCandidate = candidate;
        }
      }
      
      if (bestCandidate) {
        selected.push(bestCandidate);
        remaining.splice(remaining.indexOf(bestCandidate), 1);
      } else {
        break;
      }
    }
    
    return selected;
  }

  private getCorrelationBetweenSymbols(symbol1: string, symbol2: string, matrix: CorrelationMatrix): number {
    const index1 = matrix.symbols.indexOf(symbol1);
    const index2 = matrix.symbols.indexOf(symbol2);
    
    if (index1 === -1 || index2 === -1) return 0;
    
    return matrix.matrix[index1][index2];
  }

  private calculateNeutralityScore(longs: string[], shorts: string[], matrix: CorrelationMatrix): number {
    if (longs.length === 0 || shorts.length === 0) return 0;
    
    let totalCorrelation = 0;
    let count = 0;
    
    for (const longSymbol of longs) {
      for (const shortSymbol of shorts) {
        const correlation = this.getCorrelationBetweenSymbols(longSymbol, shortSymbol, matrix);
        totalCorrelation += Math.abs(correlation);
        count++;
      }
    }
    
    const avgCorrelation = count > 0 ? totalCorrelation / count : 0;
    return Math.max(0, 1 - avgCorrelation); // Higher score for lower correlation
  }

  private calculateRiskScore(positions: string[], matrix: CorrelationMatrix): number {
    if (positions.length <= 1) return 0;
    
    let totalCorrelation = 0;
    let count = 0;
    
    for (let i = 0; i < positions.length; i++) {
      for (let j = i + 1; j < positions.length; j++) {
        const correlation = this.getCorrelationBetweenSymbols(positions[i], positions[j], matrix);
        totalCorrelation += Math.abs(correlation);
        count++;
      }
    }
    
    return count > 0 ? totalCorrelation / count : 0; // Higher correlation = higher risk
  }

  private calculateDiversificationScore(positions: string[], matrix: CorrelationMatrix): number {
    const riskScore = this.calculateRiskScore(positions, matrix);
    return Math.max(0, 1 - riskScore); // Inverse of risk score
  }

  private async getCorrelationMatrix(): Promise<CorrelationMatrix | null> {
    const cached = await this.dataManager.getCorrelations();
    return cached.matrix || null;
  }
}
