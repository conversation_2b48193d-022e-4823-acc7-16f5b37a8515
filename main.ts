import { config } from "./env.ts";
import { PortfolioManager } from "./portfolio-manager.ts";
import { DataManager } from "./data-manager.ts";
import { AnalyticsEngine } from "./analytics-engine.ts";

const dataManager = new DataManager();
const analyticsEngine = new AnalyticsEngine(dataManager);
const portfolioManager = new PortfolioManager(analyticsEngine);

const main = async () => {
  console.log("🚀 Starting Autonomous Market-Neutral Trading System...");
    console.log("📋 Configuration:");
    console.log(`   • Leverage: ${config.leverage}x`);
    console.log(`   • Timeframe: ${config.timeframe} minutes (1h)`);
    console.log(`   • Rebalancing: Every ${config.rebalanceHours} hour(s)`);
    console.log(`   • Max Positions: ${config.maxPositions} (market neutral)`);
    console.log(`   • Historical Data: ${config.daysBack} days`);

  try {
    // Initialize the system
    console.log("🔧 Initializing system...");
    await dataManager.initialize();
    await analyticsEngine.initialize();
    await portfolioManager.initialize();

    // Display initial system status
    await displaySystemStatus();

    // Start continuous monitoring
    startSystemMonitoring();

    // Start the main trading loop
    console.log("🤖 Starting market-neutral portfolio management...");
    await portfolioManager.startRebalancing();

  } catch (error) {
    console.error("❌ Error in main execution:", error);
  }
};

// Enhanced system status display
const displaySystemStatus = async () => {
  console.log("\n📊 System Status:");

  try {
    // Portfolio status
    const portfolioSummary = await portfolioManager.getPortfolioSummary();
    if (portfolioSummary.portfolio) {
      const portfolio = portfolioSummary.portfolio;
      console.log(`💼 Portfolio: $${portfolio.totalBalance.toFixed(2)} total, ${portfolio.positionCount} positions`);
      console.log(`   • Long: ${portfolio.longPositions} positions`);
      console.log(`   • Short: ${portfolio.shortPositions} positions`);
      console.log(`   • Net Exposure: $${portfolio.netExposure.toFixed(2)}`);
      console.log(`   • Leverage: ${portfolio.leverage.toFixed(2)}x`);
      console.log(`   • Unrealized PnL: $${portfolio.unrealizedPnl.toFixed(2)}`);
    }

    // Analytics status
    const analysisStats = await analyticsEngine.getAnalysisStats();
    console.log(`🧠 Analytics: ${analysisStats.weightsCount} symbols with weights`);
    console.log(`   • Correlation Matrix: ${analysisStats.correlationMatrixSize} symbols`);
    console.log(`   • Price Predictions: ${analysisStats.predictionsCount} active`);

    // Cache status
    const cacheStats = analysisStats.cacheStats;
    console.log(`📂 Cache: ${cacheStats.historicalDatasets} datasets, ${cacheStats.availableSymbols} symbols`);

  } catch (error) {
    console.error("❌ Error displaying system status:", error);
  }
};

// Continuous system monitoring
const startSystemMonitoring = () => {
  setInterval(async () => {
    try {
      const now = new Date().toLocaleTimeString();
      console.log(`\n⏰ [${now}] System Health Check:`);

      // Quick portfolio check
      const portfolio = portfolioManager.getCurrentPortfolio();
      if (portfolio) {
        const neutralityRatio = Math.abs(portfolio.netExposure) / (portfolio.totalBalance || 1);
        const neutralityStatus = neutralityRatio < 0.1 ? "✅" : "⚠️";
        console.log(`${neutralityStatus} Market Neutrality: ${(neutralityRatio * 100).toFixed(1)}% deviation`);
        console.log(`💰 Balance: $${portfolio.totalBalance.toFixed(2)} | PnL: $${portfolio.unrealizedPnl.toFixed(2)}`);
      }

      // Analysis freshness check
      const lastAnalysis = analyticsEngine.getLastAnalysis();
      if (lastAnalysis) {
        const analysisAge = (Date.now() - lastAnalysis.timestamp) / 1000 / 60;
        const freshnessStatus = analysisAge < 70 ? "✅" : "⚠️";
        console.log(`${freshnessStatus} Analysis: ${analysisAge.toFixed(0)} minutes old`);
      }

    } catch (error) {
      console.error("❌ Error in system monitoring:", error);
    }
  }, 5 * 60 * 1000); // Every 5 minutes
};

// Handle graceful shutdown
const shutdown = () => {
  console.log("\n👋 Shutting down gracefully...");
  portfolioManager.stop();
  Deno.exit(0);
};

// Listen for shutdown signals
Deno.addSignalListener("SIGINT", shutdown);
Deno.addSignalListener("SIGTERM", shutdown);

if (import.meta.main) {
  main();
}
