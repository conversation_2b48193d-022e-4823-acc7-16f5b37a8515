import { DataManager } from "./data-manager.ts";
import { HistoricalAnalyzer, IndicatorPerformance } from "./historical-analyzer.ts";
import { WeightCalculator, SymbolWeights } from "./weight-calculator.ts";
import { CorrelationEngine, CorrelationMatrix, PortfolioBalance } from "./correlation-engine.ts";
import { ProbabilityCalculator, PricePrediction, ExpectedValue } from "./probability-calculator.ts";
import { config } from "./env.ts";

export interface AnalysisResult {
  timestamp: number;
  symbolCount: number;
  indicatorPerformance: IndicatorPerformance[];
  weights: Record<string, SymbolWeights>;
  correlationMatrix: CorrelationMatrix;
  predictions: PricePrediction[];
  expectedValues: ExpectedValue[];
  portfolioRecommendation: PortfolioBalance;
  summary: AnalysisSummary;
}

export interface AnalysisSummary {
  topPerformingIndicators: string[];
  bestSymbols: string[];
  marketClusters: number;
  avgCorrelation: number;
  highConfidencePredictions: number;
  expectedPortfolioReturn: number;
  riskScore: number;
}

export interface TradingSignal {
  symbol: string;
  action: 'BUY' | 'SELL' | 'HOLD';
  strength: number; // 0-1
  expectedReturn: number;
  probability: number;
  timeframe: number;
  reasoning: string[];
  riskFactors: string[];
  confidence: number;
  timestamp: number;
}

export class AnalyticsEngine {
  private dataManager: DataManager;
  private historicalAnalyzer: HistoricalAnalyzer;
  private weightCalculator: WeightCalculator;
  private correlationEngine: CorrelationEngine;
  private probabilityCalculator: ProbabilityCalculator;
  
  private readonly analysisInterval = config.rebalanceHours * 60 * 60 * 1000;
  private readonly topSymbolsCount = 50;
  private readonly daysBack = config.daysBack;
  
  private isRunning = false;
  private lastAnalysis: AnalysisResult | null = null;

  constructor(dataManager: DataManager) {
    this.dataManager = dataManager;
    this.historicalAnalyzer = new HistoricalAnalyzer(dataManager);
    this.weightCalculator = new WeightCalculator(dataManager);
    this.correlationEngine = new CorrelationEngine(dataManager);
    this.probabilityCalculator = new ProbabilityCalculator(dataManager);
  }

  async initialize(): Promise<void> {
    console.log("🧠 Initializing AnalyticsEngine...");
    
    // Check if we have recent analysis data
    const existingWeights = await this.dataManager.getWeights();
    const existingCorrelations = await this.dataManager.getCorrelations();
    const existingProbabilities = await this.dataManager.getProbabilities();
    
    if (Object.keys(existingWeights).length === 0 || 
        !existingCorrelations.matrix || 
        !existingProbabilities.predictions) {
      console.log("🔄 No existing analysis found, performing initial analysis...");
      await this.performFullAnalysis();
    } else {
      console.log("📊 Found existing analysis data");
    }
    
    console.log("✅ AnalyticsEngine initialized");
  }

  async performFullAnalysis(): Promise<AnalysisResult> {
    console.log("🔍 Performing full market analysis...");
    
    const startTime = Date.now();
    
    try {
      // Get top symbols by volume
      const topSymbols = await this.dataManager.getTopSymbolsByVolume(this.topSymbolsCount);
      const symbolNames = topSymbols.map(s => s.symbol);
      
      console.log(`📈 Analyzing ${symbolNames.length} top symbols...`);
      
      // Step 1: Analyze indicator performance
      console.log("1️⃣ Analyzing indicator performance...");
      const indicatorPerformance = await this.historicalAnalyzer.analyzeIndicatorPerformance(
        symbolNames, 
        this.daysBack
      );
      
      // Step 2: Calculate weights based on performance
      console.log("2️⃣ Calculating indicator weights...");
      const weights = await this.weightCalculator.calculateIndicatorWeights(indicatorPerformance);
      
      // Step 3: Calculate correlation matrix
      console.log("3️⃣ Calculating correlation matrix...");
      const correlationMatrix = await this.correlationEngine.calculateCorrelationMatrix(
        symbolNames, 
        this.daysBack
      );
      
      // Step 4: Calculate price probabilities
      console.log("4️⃣ Calculating price probabilities...");
      const predictions = await this.probabilityCalculator.calculatePriceProbabilities(
        symbolNames,
        [1] // Just 1 hour predictions for simplicity
      );
      
      // Step 5: Calculate expected values
      console.log("5️⃣ Calculating expected values...");
      const expectedValues = await this.probabilityCalculator.calculateExpectedValues(predictions);
      
      // Step 6: Generate portfolio recommendation
      console.log("6️⃣ Generating portfolio recommendation...");
      const portfolioRecommendation = await this.generatePortfolioRecommendation(
        expectedValues,
        correlationMatrix
      );
      
      // Step 7: Generate summary
      const summary = this.generateAnalysisSummary(
        indicatorPerformance,
        weights,
        correlationMatrix,
        predictions,
        expectedValues,
        portfolioRecommendation
      );
      
      const analysisResult: AnalysisResult = {
        timestamp: Date.now(),
        symbolCount: symbolNames.length,
        indicatorPerformance,
        weights,
        correlationMatrix,
        predictions,
        expectedValues,
        portfolioRecommendation,
        summary
      };
      
      this.lastAnalysis = analysisResult;
      
      const duration = (Date.now() - startTime) / 1000;
      console.log(`✅ Full analysis completed in ${duration.toFixed(1)}s`);
      
      return analysisResult;
      
    } catch (error) {
      console.error("❌ Error during full analysis:", error);
      throw error;
    }
  }

  private async generatePortfolioRecommendation(
    expectedValues: ExpectedValue[],
    correlationMatrix: CorrelationMatrix
  ): Promise<PortfolioBalance> {
    // Simple separation into long and short candidates
    const longCandidates = expectedValues
      .filter(ev => ev.expectedReturn > 0)
      .sort((a, b) => b.expectedReturn - a.expectedReturn)
      .slice(0, config.maxPositions / 2)
      .map(ev => ev.symbol);

    const shortCandidates = expectedValues
      .filter(ev => ev.expectedReturn < 0)
      .sort((a, b) => a.expectedReturn - b.expectedReturn)
      .slice(0, config.maxPositions / 2)
      .map(ev => ev.symbol);
    
    // Optimize portfolio balance using correlation engine
    return await this.correlationEngine.optimizePortfolioBalance(
      longCandidates,
      shortCandidates,
      10 // Max 10 positions
    );
  }

  private generateAnalysisSummary(
    indicatorPerformance: IndicatorPerformance[],
    weights: Record<string, SymbolWeights>,
    correlationMatrix: CorrelationMatrix,
    predictions: PricePrediction[],
    expectedValues: ExpectedValue[],
    portfolioRecommendation: PortfolioBalance
  ): AnalysisSummary {
    // Top performing indicators
    const indicatorStats = this.calculateIndicatorStats(indicatorPerformance);
    const topPerformingIndicators = Object.entries(indicatorStats)
      .sort(([,a], [,b]) => b.avgWinRate - a.avgWinRate)
      .slice(0, 3)
      .map(([indicator]) => indicator);
    
    // Best symbols by overall score
    const bestSymbols = Object.entries(weights)
      .sort(([,a], [,b]) => b.overallScore - a.overallScore)
      .slice(0, 5)
      .map(([symbol]) => symbol);
    
    // Market clusters
    const clusters = this.identifyMarketClusters(correlationMatrix);
    
    // Average correlation
    const avgCorrelation = this.calculateAverageCorrelation(correlationMatrix);
    
    // High confidence predictions
    const highConfidencePredictions = predictions.filter(p => p.confidence > 0.7).length;
    
    // Expected portfolio return
    const expectedPortfolioReturn = this.calculateExpectedPortfolioReturn(
      portfolioRecommendation,
      expectedValues
    );
    
    return {
      topPerformingIndicators,
      bestSymbols,
      marketClusters: clusters,
      avgCorrelation,
      highConfidencePredictions,
      expectedPortfolioReturn,
      riskScore: portfolioRecommendation.riskScore
    };
  }

  private calculateIndicatorStats(performance: IndicatorPerformance[]): Record<string, any> {
    const stats: Record<string, any> = {};
    
    const groupedByIndicator = performance.reduce((acc, perf) => {
      if (!acc[perf.indicator]) acc[perf.indicator] = [];
      acc[perf.indicator].push(perf);
      return acc;
    }, {} as Record<string, IndicatorPerformance[]>);
    
    for (const [indicator, perfs] of Object.entries(groupedByIndicator)) {
      stats[indicator] = {
        avgWinRate: perfs.reduce((sum, p) => sum + p.winRate, 0) / perfs.length,
        avgReturn: perfs.reduce((sum, p) => sum + p.avgReturn, 0) / perfs.length,
        avgSharpe: perfs.reduce((sum, p) => sum + p.sharpeRatio, 0) / perfs.length,
        symbolCount: perfs.length
      };
    }
    
    return stats;
  }

  private identifyMarketClusters(correlationMatrix: CorrelationMatrix): number {
    // Simplified cluster counting - count groups of highly correlated symbols
    const visited = new Set<number>();
    let clusters = 0;
    
    for (let i = 0; i < correlationMatrix.symbols.length; i++) {
      if (visited.has(i)) continue;
      
      const cluster = new Set([i]);
      const queue = [i];
      visited.add(i);
      
      while (queue.length > 0) {
        const current = queue.shift()!;
        
        for (let j = 0; j < correlationMatrix.symbols.length; j++) {
          if (!visited.has(j) && Math.abs(correlationMatrix.matrix[current][j]) > 0.6) {
            visited.add(j);
            cluster.add(j);
            queue.push(j);
          }
        }
      }
      
      if (cluster.size >= 2) clusters++;
    }
    
    return clusters;
  }

  private calculateAverageCorrelation(correlationMatrix: CorrelationMatrix): number {
    let sum = 0;
    let count = 0;
    
    for (let i = 0; i < correlationMatrix.matrix.length; i++) {
      for (let j = i + 1; j < correlationMatrix.matrix[i].length; j++) {
        sum += Math.abs(correlationMatrix.matrix[i][j]);
        count++;
      }
    }
    
    return count > 0 ? sum / count : 0;
  }

  private calculateExpectedPortfolioReturn(
    portfolio: PortfolioBalance,
    expectedValues: ExpectedValue[]
  ): number {
    const allPositions = [...portfolio.longPositions, ...portfolio.shortPositions];
    const relevantValues = expectedValues.filter(ev => allPositions.includes(ev.symbol));
    
    if (relevantValues.length === 0) return 0;
    
    const weightedReturn = relevantValues.reduce((sum, ev) => {
      const weight = 1 / allPositions.length; // Equal weight for simplicity
      const adjustedReturn = portfolio.longPositions.includes(ev.symbol) 
        ? ev.expectedReturn 
        : -ev.expectedReturn; // Invert for short positions
      
      return sum + (weight * adjustedReturn * ev.probability);
    }, 0);
    
    return weightedReturn;
  }

  async generateTradingSignals(): Promise<TradingSignal[]> {
    console.log("🎯 Generating enhanced trading signals...");

    // Check if we need fresh analysis
    const analysisAge = this.lastAnalysis ? Date.now() - this.lastAnalysis.timestamp : Infinity;
    const needsRefresh = !this.lastAnalysis || analysisAge > (60 * 60 * 1000); // 1 hour

    if (needsRefresh) {
      console.log("🔄 Refreshing analysis for enhanced signals...");
      await this.performFullAnalysis();
    }

    const signals: TradingSignal[] = [];
    const { predictions, weights, portfolioRecommendation, correlationMatrix } = this.lastAnalysis!;

    // Enhanced signal generation with market-neutral focus
    const longSignals = await this.generateEnhancedPositionSignals(
      portfolioRecommendation.longPositions,
      'BUY',
      predictions,
      weights,
      correlationMatrix
    );

    const shortSignals = await this.generateEnhancedPositionSignals(
      portfolioRecommendation.shortPositions,
      'SELL',
      predictions,
      weights,
      correlationMatrix
    );

    // Combine and balance signals for market neutrality
    signals.push(...longSignals, ...shortSignals);

    // Apply correlation-based filtering to avoid over-concentration
    const filteredSignals = this.filterSignalsByCorrelation(signals, correlationMatrix);

    // Sort by enhanced scoring (strength * confidence * expected return)
    filteredSignals.sort((a, b) => {
      const scoreA = a.strength * a.confidence * Math.abs(a.expectedReturn);
      const scoreB = b.strength * b.confidence * Math.abs(b.expectedReturn);
      return scoreB - scoreA;
    });

    // Ensure market neutrality in final selection
    const balancedSignals = this.balanceSignalsForNeutrality(
      filteredSignals.filter(s => s.action === 'BUY'),
      filteredSignals.filter(s => s.action === 'SELL')
    );

    console.log(`✅ Generated ${balancedSignals.length} enhanced trading signals (${balancedSignals.filter(s => s.action === 'BUY').length} long, ${balancedSignals.filter(s => s.action === 'SELL').length} short)`);

    // Save signals to timestamped file
    await this.saveSignalsToFile(balancedSignals);

    return balancedSignals;
  }

  private async generateEnhancedPositionSignals(
    symbols: string[],
    action: 'BUY' | 'SELL',
    predictions: PricePrediction[],
    weights: Record<string, SymbolWeights>,
    correlationMatrix: CorrelationMatrix
  ): Promise<TradingSignal[]> {
    const signals: TradingSignal[] = [];

    for (const symbol of symbols) {
      // Use 1h prediction for more responsive signals
      const prediction = predictions.find(p => p.symbol === symbol && p.timeframe === 1);
      const symbolWeight = weights[symbol];

      if (prediction && symbolWeight && prediction.confidence > 0.5) {
        // Enhanced strength calculation using multiple factors
        const baseStrength = symbolWeight.overallScore;
        const probabilityBonus = prediction.probability > 0.7 ? 0.1 : 0;
        const confidenceBonus = prediction.confidence > 0.8 ? 0.1 : 0;
        const enhancedStrength = Math.min(1, baseStrength + probabilityBonus + confidenceBonus);

        // Adjust expected return based on action
        const adjustedReturn = action === 'SELL' ? -Math.abs(prediction.expectedReturn) : Math.abs(prediction.expectedReturn);

        signals.push({
          symbol,
          action,
          strength: enhancedStrength,
          expectedReturn: adjustedReturn,
          probability: prediction.probability,
          timeframe: prediction.timeframe,
          reasoning: [
            `Enhanced ${action} signal with ${(prediction.confidence * 100).toFixed(1)}% confidence`,
            `Expected ${prediction.direction} movement: ${(prediction.probability * 100).toFixed(1)}%`,
            ...prediction.supportingFactors,
            `Weighted symbol score: ${(enhancedStrength * 100).toFixed(1)}%`
          ],
          riskFactors: prediction.riskFactors,
          confidence: prediction.confidence,
          timestamp: Date.now()
        });
      }
    }

    return signals;
  }

  private filterSignalsByCorrelation(signals: TradingSignal[], correlationMatrix: CorrelationMatrix): TradingSignal[] {
    if (!correlationMatrix.matrix || signals.length <= 1) return signals;

    const filtered: TradingSignal[] = [];
    const maxCorrelation = 0.7; // Maximum allowed correlation between selected signals

    // Sort by strength first
    const sortedSignals = [...signals].sort((a, b) => (b.strength * b.confidence) - (a.strength * a.confidence));

    for (const signal of sortedSignals) {
      let shouldAdd = true;

      // Check correlation with already selected signals
      for (const selectedSignal of filtered) {
        const correlation = this.getSymbolCorrelation(signal.symbol, selectedSignal.symbol, correlationMatrix);

        if (Math.abs(correlation) > maxCorrelation) {
          shouldAdd = false;
          break;
        }
      }

      if (shouldAdd) {
        filtered.push(signal);
      }
    }

    return filtered;
  }

  private getSymbolCorrelation(symbol1: string, symbol2: string, correlationMatrix: CorrelationMatrix): number {
    const index1 = correlationMatrix.symbols.indexOf(symbol1);
    const index2 = correlationMatrix.symbols.indexOf(symbol2);

    if (index1 === -1 || index2 === -1) return 0;

    return correlationMatrix.matrix[index1][index2];
  }

  private balanceSignalsForNeutrality(longSignals: TradingSignal[], shortSignals: TradingSignal[]): TradingSignal[] {
    const maxPositionsPerSide = config.maxPositions / 2; // Maximum positions per side

    // Select top signals from each side
    const selectedLongs = longSignals.slice(0, maxPositionsPerSide);
    const selectedShorts = shortSignals.slice(0, maxPositionsPerSide);

    // Ensure equal number of long and short positions for market neutrality
    const minCount = Math.min(selectedLongs.length, selectedShorts.length);

    return [
      ...selectedLongs.slice(0, minCount),
      ...selectedShorts.slice(0, minCount)
    ];
  }

  async startContinuousAnalysis(): Promise<void> {
    if (this.isRunning) {
      console.log("🏃‍♂️ Continuous analysis already running");
      return;
    }
    
    this.isRunning = true;
    console.log("🔄 Starting continuous analysis...");
    
    while (this.isRunning) {
      try {
        await this.performFullAnalysis();
        console.log(`⏰ Next analysis in ${this.analysisInterval / 1000 / 60} minutes`);
        await new Promise(resolve => setTimeout(resolve, this.analysisInterval));
      } catch (error) {
        console.error("❌ Error in continuous analysis:", error);
        await new Promise(resolve => setTimeout(resolve, 60000)); // Wait 1 minute on error
      }
    }
  }

  stopContinuousAnalysis(): void {
    this.isRunning = false;
    console.log("⏹️ Stopping continuous analysis");
  }

  private async saveSignalsToFile(signals: TradingSignal[]): Promise<void> {
    try {
      // Create signals directory if it doesn't exist
      const signalsDir = "./data/signals";
      try {
        await Deno.mkdir(signalsDir, { recursive: true });
      } catch (error) {
        if (!(error instanceof Deno.errors.AlreadyExists)) {
          throw error;
        }
      }

      // Generate ISO timestamp filename
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const filename = `${signalsDir}/signals_${timestamp}.json`;

      // Prepare signal data with metadata
      const signalData = {
        timestamp: new Date().toISOString(),
        totalSignals: signals.length,
        longSignals: signals.filter(s => s.action === 'BUY').length,
        shortSignals: signals.filter(s => s.action === 'SELL').length,
        config: {
          leverage: config.leverage,
          timeframe: config.timeframe,
          maxPositions: config.maxPositions
        },
        signals: signals.map(signal => ({
          symbol: signal.symbol,
          action: signal.action,
          strength: signal.strength,
          expectedReturn: signal.expectedReturn,
          probability: signal.probability,
          timeframe: signal.timeframe,
          reasoning: signal.reasoning,
          riskFactors: signal.riskFactors,
          confidence: signal.confidence,
          timestamp: signal.timestamp
        }))
      };

      // Save to file
      await Deno.writeTextFile(filename, JSON.stringify(signalData, null, 2));
      console.log(`💾 Signals saved to: ${filename}`);

    } catch (error) {
      console.error("❌ Error saving signals to file:", error);
    }
  }

  getLastAnalysis(): AnalysisResult | null {
    return this.lastAnalysis;
  }

  async getAnalysisStats(): Promise<any> {
    const weights = await this.dataManager.getWeights();
    const correlations = await this.dataManager.getCorrelations();
    const probabilities = await this.dataManager.getProbabilities();
    
    return {
      weightsCount: Object.keys(weights).length,
      correlationMatrixSize: correlations.matrix?.symbols?.length || 0,
      predictionsCount: probabilities.predictions?.length || 0,
      lastAnalysisTime: this.lastAnalysis?.timestamp || 0,
      cacheStats: this.dataManager.getCacheStats()
    };
  }
}
