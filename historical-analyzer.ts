import { OHLCV, TechnicalIndicators } from "./technical-indicators.ts";
import { DataManager } from "./data-manager.ts";

export interface IndicatorPerformance {
  indicator: string;
  symbol: string;
  accuracy: number;
  profitability: number;
  winRate: number;
  avgReturn: number;
  maxDrawdown: number;
  sharpeRatio: number;
  totalTrades: number;
  sampleSize: number;
}

export interface BacktestResult {
  symbol: string;
  strategy: string;
  totalReturn: number;
  winRate: number;
  maxDrawdown: number;
  sharpeRatio: number;
  trades: TradeResult[];
  performance: IndicatorPerformance[];
}

export interface TradeResult {
  entryTime: number;
  exitTime: number;
  entryPrice: number;
  exitPrice: number;
  direction: 'LONG' | 'SHORT';
  return: number;
  indicator: string;
  confidence: number;
}

export interface PriceMovementPattern {
  timeframe: number; // hours ahead
  direction: 'UP' | 'DOWN';
  magnitude: number; // percentage change
  probability: number;
  conditions: Record<string, any>;
}

export class HistoricalAnalyzer {
  private dataManager: DataManager;
  private technicalIndicators: TechnicalIndicators;
  private readonly lookbackPeriods = [1, 2, 4, 8, 12, 24]; // hours ahead to analyze

  constructor(dataManager: DataManager) {
    this.dataManager = dataManager;
    this.technicalIndicators = new TechnicalIndicators();
  }

  async analyzeIndicatorPerformance(symbols: string[], daysBack: number = 30): Promise<IndicatorPerformance[]> {
    console.log("🔍 Analyzing indicator performance across symbols...");
    
    const allPerformance: IndicatorPerformance[] = [];
    
    for (const symbol of symbols) {
      console.log(`📊 Analyzing ${symbol}...`);
      
      const historicalData = await this.dataManager.getHistoricalData(symbol, daysBack);
      if (historicalData.length < 100) {
        console.log(`⚠️ Insufficient data for ${symbol}, skipping...`);
        continue;
      }

      const symbolPerformance = await this.analyzeSymbolIndicators(symbol, historicalData);
      allPerformance.push(...symbolPerformance);
    }

    return allPerformance;
  }

  private async analyzeSymbolIndicators(symbol: string, data: OHLCV[]): Promise<IndicatorPerformance[]> {
    const closes = data.map(d => d.close);
    const highs = data.map(d => d.high);
    const lows = data.map(d => d.low);
    const performance: IndicatorPerformance[] = [];

    // Analyze RSI performance
    const rsiPerformance = this.analyzeRSIPerformance(symbol, data, closes);
    if (rsiPerformance) performance.push(rsiPerformance);

    // Analyze MACD performance
    const macdPerformance = this.analyzeMACDPerformance(symbol, data, closes);
    if (macdPerformance) performance.push(macdPerformance);

    // Analyze Bollinger Bands performance
    const bbPerformance = this.analyzeBollingerPerformance(symbol, data, closes);
    if (bbPerformance) performance.push(bbPerformance);

    // Analyze Moving Average performance
    const maPerformance = this.analyzeMAPerformance(symbol, data, closes);
    if (maPerformance) performance.push(maPerformance);

    return performance;
  }

  private analyzeRSIPerformance(symbol: string, data: OHLCV[], closes: number[]): IndicatorPerformance | null {
    try {
      const trades: TradeResult[] = [];
      let position: 'LONG' | 'SHORT' | null = null;
      let entryPrice = 0;
      let entryTime = 0;

      for (let i = 14; i < data.length - 1; i++) {
        const rsi = this.technicalIndicators.rsi(closes.slice(0, i + 1), 14);
        if (!rsi) continue;

        const currentPrice = closes[i];
        const nextPrice = closes[i + 1];
        const currentTime = data[i].timestamp;
        const nextTime = data[i + 1].timestamp;

        // Entry signals
        if (!position) {
          if (rsi.oversold) {
            position = 'LONG';
            entryPrice = currentPrice;
            entryTime = currentTime;
          } else if (rsi.overbought) {
            position = 'SHORT';
            entryPrice = currentPrice;
            entryTime = currentTime;
          }
        }
        // Exit signals
        else {
          let shouldExit = false;
          
          if (position === 'LONG' && (rsi.overbought || rsi.value > 60)) {
            shouldExit = true;
          } else if (position === 'SHORT' && (rsi.oversold || rsi.value < 40)) {
            shouldExit = true;
          }

          if (shouldExit) {
            const returnPct = position === 'LONG' 
              ? (nextPrice - entryPrice) / entryPrice
              : (entryPrice - nextPrice) / entryPrice;

            trades.push({
              entryTime,
              exitTime: nextTime,
              entryPrice,
              exitPrice: nextPrice,
              direction: position,
              return: returnPct,
              indicator: 'RSI',
              confidence: Math.abs(50 - rsi.value) / 50 * 100
            });

            position = null;
          }
        }
      }

      return this.calculatePerformanceMetrics('RSI', symbol, trades);
    } catch (error) {
      console.error(`Error analyzing RSI for ${symbol}:`, error);
      return null;
    }
  }

  private analyzeMACDPerformance(symbol: string, data: OHLCV[], closes: number[]): IndicatorPerformance | null {
    try {
      const trades: TradeResult[] = [];
      let position: 'LONG' | 'SHORT' | null = null;
      let entryPrice = 0;
      let entryTime = 0;
      let prevMACD: any = null;

      for (let i = 26; i < data.length - 1; i++) {
        const macd = this.technicalIndicators.macd(closes.slice(0, i + 1), 12, 26, 9);
        if (!macd || !prevMACD) {
          prevMACD = macd;
          continue;
        }

        const currentPrice = closes[i];
        const nextPrice = closes[i + 1];
        const currentTime = data[i].timestamp;
        const nextTime = data[i + 1].timestamp;

        // Entry signals - MACD crossovers
        if (!position) {
          if (prevMACD.histogram <= 0 && macd.histogram > 0) {
            position = 'LONG';
            entryPrice = currentPrice;
            entryTime = currentTime;
          } else if (prevMACD.histogram >= 0 && macd.histogram < 0) {
            position = 'SHORT';
            entryPrice = currentPrice;
            entryTime = currentTime;
          }
        }
        // Exit signals
        else {
          let shouldExit = false;
          
          if (position === 'LONG' && macd.histogram < 0) {
            shouldExit = true;
          } else if (position === 'SHORT' && macd.histogram > 0) {
            shouldExit = true;
          }

          if (shouldExit) {
            const returnPct = position === 'LONG' 
              ? (nextPrice - entryPrice) / entryPrice
              : (entryPrice - nextPrice) / entryPrice;

            trades.push({
              entryTime,
              exitTime: nextTime,
              entryPrice,
              exitPrice: nextPrice,
              direction: position,
              return: returnPct,
              indicator: 'MACD',
              confidence: Math.abs(macd.histogram) * 100
            });

            position = null;
          }
        }

        prevMACD = macd;
      }

      return this.calculatePerformanceMetrics('MACD', symbol, trades);
    } catch (error) {
      console.error(`Error analyzing MACD for ${symbol}:`, error);
      return null;
    }
  }

  private analyzeBollingerPerformance(symbol: string, data: OHLCV[], closes: number[]): IndicatorPerformance | null {
    try {
      const trades: TradeResult[] = [];
      let position: 'LONG' | 'SHORT' | null = null;
      let entryPrice = 0;
      let entryTime = 0;

      for (let i = 20; i < data.length - 1; i++) {
        const bb = this.technicalIndicators.bollingerBands(closes.slice(0, i + 1), 20, 2);
        if (!bb) continue;

        const currentPrice = closes[i];
        const nextPrice = closes[i + 1];
        const currentTime = data[i].timestamp;
        const nextTime = data[i + 1].timestamp;

        // Entry signals
        if (!position) {
          if (bb.position === 'BELOW_LOWER') {
            position = 'LONG';
            entryPrice = currentPrice;
            entryTime = currentTime;
          } else if (bb.position === 'ABOVE_UPPER') {
            position = 'SHORT';
            entryPrice = currentPrice;
            entryTime = currentTime;
          }
        }
        // Exit signals
        else {
          let shouldExit = false;
          
          if (position === 'LONG' && (bb.position === 'ABOVE_UPPER' || currentPrice > bb.middle)) {
            shouldExit = true;
          } else if (position === 'SHORT' && (bb.position === 'BELOW_LOWER' || currentPrice < bb.middle)) {
            shouldExit = true;
          }

          if (shouldExit) {
            const returnPct = position === 'LONG' 
              ? (nextPrice - entryPrice) / entryPrice
              : (entryPrice - nextPrice) / entryPrice;

            const bandWidth = (bb.upper - bb.lower) / bb.middle;
            trades.push({
              entryTime,
              exitTime: nextTime,
              entryPrice,
              exitPrice: nextPrice,
              direction: position,
              return: returnPct,
              indicator: 'BollingerBands',
              confidence: bandWidth * 100
            });

            position = null;
          }
        }
      }

      return this.calculatePerformanceMetrics('BollingerBands', symbol, trades);
    } catch (error) {
      console.error(`Error analyzing Bollinger Bands for ${symbol}:`, error);
      return null;
    }
  }

  private analyzeMAPerformance(symbol: string, data: OHLCV[], closes: number[]): IndicatorPerformance | null {
    try {
      const trades: TradeResult[] = [];
      let position: 'LONG' | 'SHORT' | null = null;
      let entryPrice = 0;
      let entryTime = 0;

      for (let i = 50; i < data.length - 1; i++) {
        const sma20 = this.technicalIndicators.sma(closes.slice(0, i + 1), 20);
        const sma50 = this.technicalIndicators.sma(closes.slice(0, i + 1), 50);
        
        if (sma20.length === 0 || sma50.length === 0) continue;

        const currentSMA20 = sma20[sma20.length - 1];
        const currentSMA50 = sma50[sma50.length - 1];
        const prevSMA20 = sma20[sma20.length - 2];
        const prevSMA50 = sma50[sma50.length - 2];

        const currentPrice = closes[i];
        const nextPrice = closes[i + 1];
        const currentTime = data[i].timestamp;
        const nextTime = data[i + 1].timestamp;

        // Entry signals - MA crossovers
        if (!position) {
          if (prevSMA20 <= prevSMA50 && currentSMA20 > currentSMA50) {
            position = 'LONG';
            entryPrice = currentPrice;
            entryTime = currentTime;
          } else if (prevSMA20 >= prevSMA50 && currentSMA20 < currentSMA50) {
            position = 'SHORT';
            entryPrice = currentPrice;
            entryTime = currentTime;
          }
        }
        // Exit signals
        else {
          let shouldExit = false;
          
          if (position === 'LONG' && currentSMA20 < currentSMA50) {
            shouldExit = true;
          } else if (position === 'SHORT' && currentSMA20 > currentSMA50) {
            shouldExit = true;
          }

          if (shouldExit) {
            const returnPct = position === 'LONG' 
              ? (nextPrice - entryPrice) / entryPrice
              : (entryPrice - nextPrice) / entryPrice;

            const spread = Math.abs(currentSMA20 - currentSMA50) / currentSMA50;
            trades.push({
              entryTime,
              exitTime: nextTime,
              entryPrice,
              exitPrice: nextPrice,
              direction: position,
              return: returnPct,
              indicator: 'MovingAverage',
              confidence: spread * 100
            });

            position = null;
          }
        }
      }

      return this.calculatePerformanceMetrics('MovingAverage', symbol, trades);
    } catch (error) {
      console.error(`Error analyzing Moving Averages for ${symbol}:`, error);
      return null;
    }
  }

  private calculatePerformanceMetrics(indicator: string, symbol: string, trades: TradeResult[]): IndicatorPerformance | null {
    if (trades.length === 0) return null;

    const returns = trades.map(t => t.return);
    const winningTrades = trades.filter(t => t.return > 0);
    
    const totalReturn = returns.reduce((sum, r) => sum + r, 0);
    const avgReturn = totalReturn / trades.length;
    const winRate = winningTrades.length / trades.length;
    
    // Calculate max drawdown
    let maxDrawdown = 0;
    let peak = 0;
    let cumReturn = 0;
    
    for (const ret of returns) {
      cumReturn += ret;
      if (cumReturn > peak) peak = cumReturn;
      const drawdown = peak - cumReturn;
      if (drawdown > maxDrawdown) maxDrawdown = drawdown;
    }

    // Calculate Sharpe ratio (simplified)
    const returnStdDev = Math.sqrt(
      returns.reduce((sum, r) => sum + Math.pow(r - avgReturn, 2), 0) / trades.length
    );
    const sharpeRatio = returnStdDev > 0 ? avgReturn / returnStdDev : 0;

    return {
      indicator,
      symbol,
      accuracy: winRate,
      profitability: totalReturn,
      winRate,
      avgReturn,
      maxDrawdown,
      sharpeRatio,
      totalTrades: trades.length,
      sampleSize: trades.length
    };
  }

  async analyzePriceMovementPatterns(symbols: string[], daysBack: number = 30): Promise<PriceMovementPattern[]> {
    console.log("🔮 Analyzing price movement patterns...");
    
    const patterns: PriceMovementPattern[] = [];
    
    for (const symbol of symbols) {
      const historicalData = await this.dataManager.getHistoricalData(symbol, daysBack);
      if (historicalData.length < 100) continue;

      const symbolPatterns = this.analyzeSymbolPatterns(historicalData);
      patterns.push(...symbolPatterns);
    }

    return patterns;
  }

  private analyzeSymbolPatterns(data: OHLCV[]): PriceMovementPattern[] {
    const patterns: PriceMovementPattern[] = [];
    const closes = data.map(d => d.close);

    for (const lookback of this.lookbackPeriods) {
      for (let i = 50; i < data.length - lookback; i++) {
        const currentPrice = closes[i];
        const futurePrice = closes[i + lookback];
        const priceChange = (futurePrice - currentPrice) / currentPrice;

        // Calculate current market conditions
        const rsi = this.technicalIndicators.rsi(closes.slice(0, i + 1), 14);
        const macd = this.technicalIndicators.macd(closes.slice(0, i + 1), 12, 26, 9);
        const bb = this.technicalIndicators.bollingerBands(closes.slice(0, i + 1), 20, 2);

        if (!rsi || !macd || !bb) continue;

        const conditions = {
          rsi: rsi.value,
          macdHistogram: macd.histogram,
          bollingerPosition: bb.position,
          priceVsBB: (currentPrice - bb.middle) / bb.middle
        };

        patterns.push({
          timeframe: lookback,
          direction: priceChange > 0 ? 'UP' : 'DOWN',
          magnitude: Math.abs(priceChange),
          probability: 0, // Will be calculated later
          conditions
        });
      }
    }

    return patterns;
  }
}
