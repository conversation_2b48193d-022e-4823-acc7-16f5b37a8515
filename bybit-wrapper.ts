import { RestClientV5 } from "bybit-api";
import { bybit } from "./env.ts";

export interface RateLimitInfo {
  remainingRequests: number;
  maxRequests: number;
  resetAtTimestamp: number;
}

export interface RequestLog {
  timestamp: number;
  method: string;
  params: any;
  success: boolean;
  response?: any;
  error?: any;
  duration: number;
  rateLimitInfo?: RateLimitInfo;
}

export class BybitWrapper {
  private client: RestClientV5;
  private lastRateLimit: RateLimitInfo | null = null;
  private requestLogs: RequestLog[] = [];

  constructor() {
    this.client = new RestClientV5({
      key: bybit.apiKey,
      secret: bybit.apiSecret,
      testnet: bybit.demo,
      demoTrading: bybit.demo,
      parseAPIRateLimits: true,
    });

    // Clean up logs directory on startup
    this.cleanupLogsDirectory();
  }

  private async cleanupLogsDirectory(): Promise<void> {
    try {
      console.log("🧹 Cleaning up logs directory...");

      // Remove the entire logs directory
      try {
        await Deno.remove('./logs', { recursive: true });
      } catch {
        // Directory might not exist, that's fine
      }

      // Recreate the logs directory
      await Deno.mkdir('./logs', { recursive: true });

      console.log("✅ Logs directory cleaned");
    } catch (error) {
      console.warn("⚠️ Failed to cleanup logs directory:", error);
    }
  }

  private async handleRateLimit(): Promise<void> {
    if (this.lastRateLimit) {
      const now = Date.now();
      
      // If we're close to the limit, wait until reset
      if (this.lastRateLimit.remainingRequests <= 2) {
        const waitTime = this.lastRateLimit.resetAtTimestamp - now;
        if (waitTime > 0 && waitTime < 60000) { // Max 1 minute wait
          console.log(`⏳ Rate limit reached, waiting ${waitTime}ms...`);
          await new Promise(resolve => setTimeout(resolve, waitTime + 100));
        }
      }
    }
  }

  private updateRateLimit(response: any): void {
    if (response?.rateLimitApi) {
      this.lastRateLimit = response.rateLimitApi;
    }
  }

  private async logRequest(method: string, params: any, apiCall: () => Promise<any>): Promise<any> {
    const startTime = Date.now();
    const logEntry: RequestLog = {
      timestamp: startTime,
      method,
      params: JSON.parse(JSON.stringify(params)), // Deep clone to avoid reference issues
      success: false,
      duration: 0,
    };

    try {
      console.log(`📡 API Request: ${method}`, params);
      const response = await apiCall();

      // Check if API returned an error (retCode != 0 or retMsg != "OK")
      const isApiError = response?.retCode !== 0 || (response?.retMsg && response.retMsg !== "OK");

      if (isApiError) {
        logEntry.success = false;
        logEntry.error = {
          message: response?.retMsg || 'API Error',
          retCode: response?.retCode,
          retMsg: response?.retMsg
        };
        logEntry.duration = Date.now() - startTime;
        logEntry.rateLimitInfo = this.lastRateLimit ? { ...this.lastRateLimit } : undefined;

        console.error(`❌ API Error: ${method} (${logEntry.duration}ms)`, {
          retCode: response?.retCode,
          retMsg: response?.retMsg
        });

        this.saveLogToFile(logEntry);

        // Throw an error for API-level failures
        const error = new Error(`API Error: ${response?.retMsg || 'Unknown API error'}`);
        (error as any).retCode = response?.retCode;
        (error as any).retMsg = response?.retMsg;
        throw error;
      }

      logEntry.success = true;
      // Store only essential response data instead of full response
      logEntry.response = {
        retCode: response?.retCode,
        retMsg: response?.retMsg,
        resultSize: response?.result ? Object.keys(response.result).length : 0,
        hasResult: !!response?.result,
        orderId: response?.result?.orderId || null,
        listLength: Array.isArray(response?.result?.list) ? response.result.list.length : 0
      };
      logEntry.duration = Date.now() - startTime;
      logEntry.rateLimitInfo = this.lastRateLimit ? { ...this.lastRateLimit } : undefined;

      console.log(`✅ API Success: ${method} (${logEntry.duration}ms)`, {
        rateLimitRemaining: this.lastRateLimit?.remainingRequests,
        retCode: response?.retCode,
        retMsg: response?.retMsg
      });

      this.saveLogToFile(logEntry);
      return response;

    } catch (error) {
      // Handle HTTP-level errors or re-thrown API errors
      if (!logEntry.error) {
        logEntry.success = false;
        logEntry.error = {
          message: error?.message || 'Unknown error',
          name: error?.name || 'Error',
          code: error?.code || null,
          retCode: (error as any)?.retCode || null,
          retMsg: (error as any)?.retMsg || null
        };
        logEntry.duration = Date.now() - startTime;
        logEntry.rateLimitInfo = this.lastRateLimit ? { ...this.lastRateLimit } : undefined;

        console.error(`❌ API Error: ${method} (${logEntry.duration}ms)`, error);

        this.saveLogToFile(logEntry);
      }

      throw error;
    }
  }

  private async saveLogToFile(logEntry: RequestLog): Promise<void> {
    try {
      // Ensure logs directory exists
      try {
        await Deno.mkdir('./logs', { recursive: true });
      } catch {
        // Directory might already exist
      }

      const filename = `./logs/bybit-requests-${new Date().toISOString().split('T')[0]}.json`;

      // Read existing logs or create empty array
      let existingLogs: RequestLog[] = [];
      try {
        const existingContent = await Deno.readTextFile(filename);
        existingLogs = JSON.parse(existingContent);
      } catch {
        // File doesn't exist or is empty, start with empty array
      }

      // Add new log entry
      existingLogs.push(logEntry);

      // Keep only last 1000 entries to prevent file from growing too large
      if (existingLogs.length > 1000) {
        existingLogs = existingLogs.slice(-1000);
      }

      // Write back to file
      await Deno.writeTextFile(filename, JSON.stringify(existingLogs, null, 2));

    } catch (error) {
      console.warn(`⚠️ Failed to save request log:`, error);
    }
  }

  async getInstruments(): Promise<any[]> {
    const params = { category: 'linear' as const, limit: 1000 };
    const response = await this.logRequest('getInstrumentsInfo', params, async () => {
      await this.handleRateLimit();
      const result = await this.client.getInstrumentsInfo(params);
      this.updateRateLimit(result);
      return result;
    });

    return response.result?.list?.filter(instrument =>
      instrument.status === 'Trading' &&
      instrument.quoteCoin === 'USDT'
    ) || [];
  }

  async getKlines(symbol: string, interval: string, limit: number = 1000, end?: number): Promise<any[]> {
    const params = { category: 'linear' as const, symbol, interval: interval as any, limit, end };
    const response = await this.logRequest('getKline', params, async () => {
      await this.handleRateLimit();
      const result = await this.client.getKline(params);
      this.updateRateLimit(result);
      return result;
    });

    return response.result?.list || [];
  }

  async getTickers(): Promise<any[]> {
    const params = { category: 'linear' as const };
    const response = await this.logRequest('getTickers', params, async () => {
      await this.handleRateLimit();
      const result = await this.client.getTickers(params);
      this.updateRateLimit(result);
      return result;
    });

    return response.result?.list || [];
  }

  async getWalletBalance(): Promise<any> {
    const params = { accountType: 'UNIFIED' as const };
    const response = await this.logRequest('getWalletBalance', params, async () => {
      await this.handleRateLimit();
      const result = await this.client.getWalletBalance(params);
      this.updateRateLimit(result);
      return result;
    });

    return response.result?.list?.[0];
  }

  async getPositions(): Promise<any[]> {
    const params = { category: 'linear' as const, settleCoin: 'USDT' };
    const response = await this.logRequest('getPositionInfo', params, async () => {
      await this.handleRateLimit();
      const result = await this.client.getPositionInfo(params);
      this.updateRateLimit(result);
      return result;
    });

    return response.result?.list || [];
  }

  async setLeverage(symbol: string, leverage: number): Promise<void> {
    const params = {
      category: 'linear' as const,
      symbol,
      buyLeverage: leverage.toString(),
      sellLeverage: leverage.toString()
    };
    await this.logRequest('setLeverage', params, async () => {
      await this.handleRateLimit();
      const result = await this.client.setLeverage(params);
      this.updateRateLimit(result);
      return result;
    });
  }

  async submitOrder(params: {
    symbol: string;
    side: 'Buy' | 'Sell';
    orderType: 'Market';
    qty: string;
    reduceOnly?: boolean;
    timeInForce?: 'IOC';
  }): Promise<any> {
    const fullParams = { category: 'linear' as const, ...params };
    const response = await this.logRequest('submitOrder', fullParams, async () => {
      await this.handleRateLimit();
      const result = await this.client.submitOrder(fullParams);
      this.updateRateLimit(result);
      return result;
    });

    return response.result;
  }

  getRateLimitInfo(): RateLimitInfo | null {
    return this.lastRateLimit;
  }
}
