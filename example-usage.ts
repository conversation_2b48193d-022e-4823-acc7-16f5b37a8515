import { RestClientV5 } from "bybit-api";
import { bybit } from "./env.ts";
import { TechnicalIndicators } from "./technical-indicators.ts";
import { MarketScanner } from "./market-scanner.ts";
import { StrategyEngine, TradingSignal } from "./strategy-engine.ts";
import { RateLimiter } from "./rate-limiter.ts";

/**
 * Example usage of the Bybit Perpetual Futures Scanner
 * This demonstrates various ways to use the algorithm
 */

const bybitClient = new RestClientV5({
  key: bybit.apiKey,
  secret: bybit.apiSecret,
  testnet: bybit.demo,
  demoTrading: bybit.demo,
  parseAPIRateLimits: true,
});

const rateLimiter = new RateLimiter({
  requestsPerSecond: 8,  // Conservative rate limiting
  requestsPerMinute: 80,
  burstLimit: 3
});

const technicalIndicators = new TechnicalIndicators();
const marketScanner = new MarketScanner(bybitClient, rateLimiter);
const strategyEngine = new StrategyEngine(technicalIndicators);

/**
 * Example 1: Quick scan of top volume symbols
 */
async function quickScan() {
  console.log("\n🚀 Quick Scan Example");
  console.log("=" .repeat(40));
  
  try {
    // Get top 10 symbols by volume
    const topSymbols = await marketScanner.getTopSymbolsByVolume(10);
    console.log(`Found ${topSymbols.length} top volume symbols`);
    
    // Scan for signals
    const signals = await marketScanner.scanAllSymbols(topSymbols, strategyEngine, {
      interval: '4h',
      limit: 50,
      minVolume: 5000000, // 5M USDT minimum volume
      onProgress: (completed, total, symbol) => {
        console.log(`📊 Progress: ${completed}/${total} - Analyzing ${symbol}`);
      }
    });
    
    // Display results
    displaySignals(signals);
    
  } catch (error) {
    console.error("❌ Error in quick scan:", error);
  }
}

/**
 * Example 2: Detailed analysis of specific symbols
 */
async function detailedAnalysis() {
  console.log("\n🔍 Detailed Analysis Example");
  console.log("=" .repeat(40));
  
  const symbols = ['BTCUSDT', 'ETHUSDT', 'SOLUSDT', 'ADAUSDT', 'DOTUSDT'];
  
  for (const symbol of symbols) {
    try {
      console.log(`\n📈 Analyzing ${symbol}...`);
      
      const marketData = await marketScanner.getMarketData(symbol, '1h', 100);
      if (!marketData) {
        console.log(`❌ No data available for ${symbol}`);
        continue;
      }
      
      const signal = strategyEngine.analyzeSymbol(marketData);
      
      console.log(`Symbol: ${signal.symbol}`);
      console.log(`Signal: ${signal.signal} (${signal.confidence}% confidence)`);
      console.log(`Reason: ${signal.reason}`);
      console.log(`Price: $${signal.price.toFixed(4)}`);
      console.log(`24h Change: ${signal.priceChange24h.toFixed(2)}%`);
      console.log(`24h Volume: $${(signal.volume24h / 1000000).toFixed(2)}M`);
      
      if (signal.indicators.rsi) {
        console.log(`RSI: ${signal.indicators.rsi.toFixed(2)}`);
      }
      
      if (signal.indicators.macd) {
        console.log(`MACD: ${signal.indicators.macd.macd.toFixed(4)} | Signal: ${signal.indicators.macd.signal.toFixed(4)}`);
      }
      
      if (signal.indicators.bollinger) {
        console.log(`Bollinger: ${signal.indicators.bollinger.position} | Price: $${signal.indicators.bollinger.price.toFixed(4)}`);
      }
      
      console.log(`Trend: ${signal.indicators.trend}`);
      
    } catch (error) {
      console.error(`❌ Error analyzing ${symbol}:`, error);
    }
  }
}

/**
 * Example 3: Custom strategy configuration
 */
async function customStrategy() {
  console.log("\n⚙️ Custom Strategy Example");
  console.log("=" .repeat(40));
  
  // Create strategy with custom parameters
  const customStrategyEngine = new StrategyEngine(technicalIndicators, {
    rsiOversold: 25,      // More aggressive oversold level
    rsiOverbought: 75,    // More aggressive overbought level
    minConfidence: 70,    // Higher confidence threshold
    minVolume: 10000000,  // Higher volume requirement
  });
  
  try {
    const symbols = await marketScanner.getAvailableSymbols();
    const topSymbols = symbols.slice(0, 20); // Top 20 symbols
    
    const signals = await marketScanner.scanAllSymbols(topSymbols, customStrategyEngine, {
      interval: '2h',
      limit: 80,
      maxSymbols: 15,
      onProgress: (completed, total, symbol) => {
        console.log(`🔧 Custom scan: ${completed}/${total} - ${symbol}`);
      }
    });
    
    console.log(`\n🎯 Custom Strategy Results (min ${customStrategyEngine.getConfig().minConfidence}% confidence):`);
    displaySignals(signals);
    
  } catch (error) {
    console.error("❌ Error in custom strategy:", error);
  }
}

/**
 * Example 4: Continuous monitoring (demo)
 */
async function continuousMonitoring() {
  console.log("\n🔄 Continuous Monitoring Example (3 iterations)");
  console.log("=" .repeat(40));
  
  for (let i = 1; i <= 3; i++) {
    console.log(`\n📊 Scan iteration ${i}/3`);
    
    try {
      const topSymbols = await marketScanner.getTopSymbolsByVolume(8);
      const signals = await marketScanner.scanAllSymbols(topSymbols, strategyEngine, {
        interval: '1h',
        limit: 50,
        maxSymbols: 8,
        minVolume: 2000000,
      });
      
      const strongSignals = signals.filter(s => s.confidence >= 75);
      console.log(`Found ${strongSignals.length} strong signals (75%+ confidence)`);
      
      strongSignals.forEach(signal => {
        console.log(`  ${signal.signal} ${signal.symbol}: ${signal.confidence}% - ${signal.reason}`);
      });
      
      if (i < 3) {
        console.log("⏳ Waiting 30 seconds before next scan...");
        await new Promise(resolve => setTimeout(resolve, 30000));
      }
      
    } catch (error) {
      console.error(`❌ Error in iteration ${i}:`, error);
    }
  }
}

/**
 * Display trading signals in a formatted way
 */
function displaySignals(signals: TradingSignal[]) {
  const longSignals = signals.filter(s => s.signal === 'LONG');
  const shortSignals = signals.filter(s => s.signal === 'SHORT');
  const holdSignals = signals.filter(s => s.signal === 'HOLD');
  
  console.log(`\n📊 Results Summary:`);
  console.log(`🟢 LONG signals: ${longSignals.length}`);
  console.log(`🔴 SHORT signals: ${shortSignals.length}`);
  console.log(`⚪ HOLD signals: ${holdSignals.length}`);
  
  if (longSignals.length > 0) {
    console.log(`\n🟢 LONG Opportunities:`);
    longSignals.forEach(signal => {
      console.log(`  ${signal.symbol}: ${signal.confidence}% | $${signal.price.toFixed(4)} | ${signal.reason}`);
    });
  }
  
  if (shortSignals.length > 0) {
    console.log(`\n🔴 SHORT Opportunities:`);
    shortSignals.forEach(signal => {
      console.log(`  ${signal.symbol}: ${signal.confidence}% | $${signal.price.toFixed(4)} | ${signal.reason}`);
    });
  }
}

/**
 * Main function to run examples
 */
async function runExamples() {
  console.log("🚀 Bybit Perpetual Futures Scanner - Examples");
  console.log("=" .repeat(50));
  
  try {
    // Test connection
    console.log("🔗 Testing connection...");
    await bybitClient.getAccountInfo(); // Test connection
    console.log("✅ Connection successful");
    
    // Run examples
    await quickScan();
    await detailedAnalysis();
    await customStrategy();
    await continuousMonitoring();
    
    console.log("\n✅ All examples completed successfully!");
    
  } catch (error) {
    console.error("❌ Error running examples:", error);
  }
}

// Run examples if this file is executed directly
if (import.meta.main) {
  runExamples();
}

// Export functions for use in other modules
export {
  quickScan,
  detailedAnalysis,
  customStrategy,
  continuousMonitoring,
  displaySignals
};
