export interface OHLCV {
  open: number;
  high: number;
  low: number;
  close: number;
  volume: number;
  timestamp: number;
}

export interface IndicatorResult {
  value: number;
  signal?: 'BUY' | 'SELL' | 'NEUTRAL';
}

export interface RSIResult extends IndicatorResult {
  oversold: boolean;
  overbought: boolean;
}

export interface MACDResult {
  macd: number;
  signal: number;
  histogram: number;
  bullish: boolean;
  bearish: boolean;
}

export interface BollingerBandsResult {
  upper: number;
  middle: number;
  lower: number;
  position: 'ABOVE_UPPER' | 'BELOW_LOWER' | 'MIDDLE' | 'UNKNOWN';
}

export class TechnicalIndicators {
  
  /**
   * Calculate Simple Moving Average
   */
  sma(prices: number[], period: number): number[] {
    const result: number[] = [];
    
    for (let i = period - 1; i < prices.length; i++) {
      const sum = prices.slice(i - period + 1, i + 1).reduce((a, b) => a + b, 0);
      result.push(sum / period);
    }
    
    return result;
  }

  /**
   * Calculate Exponential Moving Average
   */
  ema(prices: number[], period: number): number[] {
    const result: number[] = [];
    const multiplier = 2 / (period + 1);
    
    // Start with SMA for first value
    let ema = prices.slice(0, period).reduce((a, b) => a + b, 0) / period;
    result.push(ema);
    
    for (let i = period; i < prices.length; i++) {
      ema = (prices[i] * multiplier) + (ema * (1 - multiplier));
      result.push(ema);
    }
    
    return result;
  }

  /**
   * Calculate RSI (Relative Strength Index)
   */
  rsi(prices: number[], period: number = 14): RSIResult | null {
    if (prices.length < period + 1) return null;
    
    const gains: number[] = [];
    const losses: number[] = [];
    
    for (let i = 1; i < prices.length; i++) {
      const change = prices[i] - prices[i - 1];
      gains.push(change > 0 ? change : 0);
      losses.push(change < 0 ? Math.abs(change) : 0);
    }
    
    const avgGain = gains.slice(-period).reduce((a, b) => a + b, 0) / period;
    const avgLoss = losses.slice(-period).reduce((a, b) => a + b, 0) / period;
    
    if (avgLoss === 0) return { value: 100, oversold: false, overbought: true };
    
    const rs = avgGain / avgLoss;
    const rsi = 100 - (100 / (1 + rs));
    
    return {
      value: rsi,
      oversold: rsi < 30,
      overbought: rsi > 70,
      signal: rsi < 30 ? 'BUY' : rsi > 70 ? 'SELL' : 'NEUTRAL'
    };
  }

  /**
   * Calculate MACD (Moving Average Convergence Divergence)
   */
  macd(prices: number[], fastPeriod: number = 12, slowPeriod: number = 26, signalPeriod: number = 9): MACDResult | null {
    if (prices.length < slowPeriod) return null;
    
    const fastEMA = this.ema(prices, fastPeriod);
    const slowEMA = this.ema(prices, slowPeriod);
    
    if (fastEMA.length === 0 || slowEMA.length === 0) return null;
    
    // Align arrays (slowEMA starts later)
    const startIndex = slowPeriod - fastPeriod;
    const macdLine: number[] = [];
    
    for (let i = 0; i < slowEMA.length; i++) {
      macdLine.push(fastEMA[i + startIndex] - slowEMA[i]);
    }
    
    const signalLine = this.ema(macdLine, signalPeriod);
    
    if (signalLine.length === 0) return null;
    
    const currentMacd = macdLine[macdLine.length - 1];
    const currentSignal = signalLine[signalLine.length - 1];
    const histogram = currentMacd - currentSignal;
    
    // Previous values for trend detection
    const prevMacd = macdLine.length > 1 ? macdLine[macdLine.length - 2] : currentMacd;
    const prevSignal = signalLine.length > 1 ? signalLine[signalLine.length - 2] : currentSignal;
    
    return {
      macd: currentMacd,
      signal: currentSignal,
      histogram,
      bullish: currentMacd > currentSignal && prevMacd <= prevSignal,
      bearish: currentMacd < currentSignal && prevMacd >= prevSignal
    };
  }

  /**
   * Calculate Bollinger Bands
   */
  bollingerBands(prices: number[], period: number = 20, stdDev: number = 2): BollingerBandsResult | null {
    if (prices.length < period) return null;
    
    const sma = this.sma(prices, period);
    const currentSMA = sma[sma.length - 1];
    const recentPrices = prices.slice(-period);
    
    // Calculate standard deviation
    const variance = recentPrices.reduce((sum, price) => {
      return sum + Math.pow(price - currentSMA, 2);
    }, 0) / period;
    
    const standardDeviation = Math.sqrt(variance);
    
    const upper = currentSMA + (standardDeviation * stdDev);
    const lower = currentSMA - (standardDeviation * stdDev);
    const currentPrice = prices[prices.length - 1];
    
    let position: BollingerBandsResult['position'] = 'MIDDLE';
    if (currentPrice > upper) position = 'ABOVE_UPPER';
    else if (currentPrice < lower) position = 'BELOW_LOWER';
    
    return {
      upper,
      middle: currentSMA,
      lower,
      position
    };
  }

  /**
   * Calculate Stochastic Oscillator
   */
  stochastic(highs: number[], lows: number[], closes: number[], kPeriod: number = 14, dPeriod: number = 3): { k: number; d: number; signal: 'BUY' | 'SELL' | 'NEUTRAL' } | null {
    if (highs.length < kPeriod || lows.length < kPeriod || closes.length < kPeriod) return null;
    
    const recentHighs = highs.slice(-kPeriod);
    const recentLows = lows.slice(-kPeriod);
    const currentClose = closes[closes.length - 1];
    
    const highestHigh = Math.max(...recentHighs);
    const lowestLow = Math.min(...recentLows);
    
    const k = ((currentClose - lowestLow) / (highestHigh - lowestLow)) * 100;
    
    // Calculate %D (simple moving average of %K)
    const kValues: number[] = [];
    for (let i = closes.length - dPeriod; i < closes.length; i++) {
      if (i >= kPeriod - 1) {
        const periodHighs = highs.slice(i - kPeriod + 1, i + 1);
        const periodLows = lows.slice(i - kPeriod + 1, i + 1);
        const periodHigh = Math.max(...periodHighs);
        const periodLow = Math.min(...periodLows);
        kValues.push(((closes[i] - periodLow) / (periodHigh - periodLow)) * 100);
      }
    }
    
    const d = kValues.reduce((a, b) => a + b, 0) / kValues.length;
    
    return {
      k,
      d,
      signal: k < 20 && d < 20 ? 'BUY' : k > 80 && d > 80 ? 'SELL' : 'NEUTRAL'
    };
  }

  /**
   * Calculate Average True Range (ATR) for volatility
   */
  atr(highs: number[], lows: number[], closes: number[], period: number = 14): number | null {
    if (highs.length < period + 1 || lows.length < period + 1 || closes.length < period + 1) return null;
    
    const trueRanges: number[] = [];
    
    for (let i = 1; i < highs.length; i++) {
      const tr1 = highs[i] - lows[i];
      const tr2 = Math.abs(highs[i] - closes[i - 1]);
      const tr3 = Math.abs(lows[i] - closes[i - 1]);
      trueRanges.push(Math.max(tr1, tr2, tr3));
    }
    
    // Calculate ATR as simple moving average of true ranges
    const recentTR = trueRanges.slice(-period);
    return recentTR.reduce((a, b) => a + b, 0) / period;
  }
}
